# 心理康复系统 HTTP/MQTT 双协议服务器

## 文件说明
- httpServer.exe: 主程序文件
- config.json: 配置文件
- 启动服务器.bat: 快速启动脚本

## 启动方法
1. 双击"httpServer.exe"启动服务器
2. 或者双击"启动服务器.bat"启动

## 协议支持
本服务器同时支持HTTP和MQTT两种协议接收平台下发数据：
- HTTP协议: 传统的REST API接口
- MQTT协议: 轻量级消息队列协议，支持实时推送

## 配置说明
编辑 config.json 文件可以修改：

### HTTP服务器配置
- 服务器端口（默认8888）
- 监听地址

### MQTT配置
- enabled: 是否启用MQTT功能
- broker_host: MQTT服务器地址
- broker_port: MQTT服务器端口（默认1883）
- client_id: 客户端标识
- topics: 订阅的主题配置

### 平台配置
- 平台接口地址
- 设备ID和Token
- 康复项目配置

## 接口说明

### HTTP API接口
服务器启动后，可通过以下HTTP接口访问：

#### 1. 接收康复信息
- URL: POST http://localhost:8888/api/receive_rehabilitation_info
- 用途: 接收平台下发的康复信息

#### 2. 上报播放统计
- URL: POST http://localhost:8888/api/report_play_statistics
- 用途: 上报播放时长统计

#### 3. 服务器状态
- URL: GET http://localhost:8888/api/status
- 用途: 查询服务器运行状态

#### 4. 当前就诊信息
- URL: GET http://localhost:8888/api/current_visit
- 用途: 查询当前就诊信息

### MQTT主题接口
当MQTT功能启用时，服务器会订阅以下主题：

#### 1. 接收康复信息
- 主题: rehabilitation/device_001/receive_info
- 用途: 接收平台通过MQTT推送的康复信息
- 数据格式: 与HTTP接口相同的JSON格式

#### 2. 心跳上报
- 主题: rehabilitation/device_001/heartbeat
- 用途: 设备状态心跳上报

## MQTT依赖安装
如需使用MQTT功能，请安装paho-mqtt库：
```
pip install paho-mqtt
```

## 数据处理
无论通过HTTP还是MQTT接收的数据，都使用相同的处理逻辑：
- 数据验证和保存
- 平台事件上报
- 本地文件存储

## 日志文件
程序运行时会在logs目录下按日期创建日志文件：
- logs/2025-06-07/log.txt - 2025年6月7日的日志
- logs/2025-06-08/log.txt - 2025年6月8日的日志
- 每天自动创建新的日志文件，便于日志管理和查看

## 技术支持
如有问题请联系技术支持团队。

版本: v1.0.0
