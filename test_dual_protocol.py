#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP/MQTT双协议测试脚本
测试康复系统的HTTP和MQTT双协议功能
"""

import json
import requests
import time
from datetime import datetime
import threading

# MQTT支持
try:
    import paho.mqtt.client as mqtt
    MQTT_AVAILABLE = True
    print("[INFO] MQTT库已加载")
except ImportError:
    MQTT_AVAILABLE = False
    print("[WARNING] MQTT库未安装，仅测试HTTP协议")

# 配置信息
HTTP_SERVER_URL = 'http://localhost:8888'
MQTT_BROKER_HOST = "*************"
MQTT_BROKER_PORT = 1883
MQTT_CLIENT_ID = "dual_protocol_test"
MQTT_TOPIC = "rehabilitation/device_001/receive_info"

def create_test_data(protocol_type):
    """创建测试数据"""
    return {
        "company": "双协议测试医院",
        "assessment": "音乐视频疗法量表",
        "basic_info": {
            "id": f"2024061600{protocol_type}",
            "department": "心理康复科",
            "name": f"测试患者-{protocol_type}",
            "gender": "男" if protocol_type == "1" else "女",
            "age": 30
        },
        "training_task": {
            "training_project": f"{protocol_type.upper()}协议轻音乐放松训练",
            "training_duration": 20,
            "training_frequency": 2
        },
        "training_effect": {
            "completed_project": "",
            "completed_duration": 0,
            "completed_frequency": 0
        },
        "signature": f"{protocol_type.upper()}协议测试医生",
        "report_date": datetime.now().strftime("%Y-%m-%d"),
        "source": f"{protocol_type}_protocol_test",
        "test_timestamp": datetime.now().isoformat()
    }

def test_http_protocol():
    """测试HTTP协议"""
    print("\n🌐 测试HTTP协议")
    print("-" * 40)
    
    try:
        # 1. 测试服务器状态
        print("1️⃣ 检查HTTP服务器状态...")
        response = requests.get(f"{HTTP_SERVER_URL}/api/status", timeout=5)
        if response.status_code == 200:
            print("✅ HTTP服务器运行正常")
        else:
            print(f"❌ HTTP服务器状态异常: {response.status_code}")
            return False
        
        # 2. 发送康复信息
        print("2️⃣ 发送HTTP康复信息...")
        test_data = create_test_data("http")
        
        response = requests.post(
            f"{HTTP_SERVER_URL}/api/receive_rehabilitation_info",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ HTTP康复信息发送成功")
            print(f"📄 响应: {response.json().get('message', '')}")
        else:
            print(f"❌ HTTP康复信息发送失败: {response.status_code}")
            return False
        
        # 3. 查询当前就诊信息
        print("3️⃣ 查询当前就诊信息...")
        response = requests.get(f"{HTTP_SERVER_URL}/api/current_visit", timeout=5)
        if response.status_code == 200:
            visit_info = response.json()
            print(f"✅ 查询成功: 患者={visit_info.get('patient_name', '')}")
        else:
            print(f"❌ 查询失败: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ HTTP协议测试异常: {e}")
        return False

def test_mqtt_protocol():
    """测试MQTT协议"""
    if not MQTT_AVAILABLE:
        print("\n📡 跳过MQTT协议测试 (库未安装)")
        return True
    
    print("\n📡 测试MQTT协议")
    print("-" * 40)
    
    mqtt_success = threading.Event()
    mqtt_error = threading.Event()
    
    def on_connect(client, userdata, flags, rc):
        if rc == 0:
            print("✅ MQTT连接成功")
        else:
            print(f"❌ MQTT连接失败: {rc}")
            mqtt_error.set()
    
    def on_publish(client, userdata, mid):
        print("✅ MQTT消息发布成功")
        mqtt_success.set()
    
    def on_disconnect(client, userdata, rc):
        print(f"🔌 MQTT连接断开: {rc}")
    
    try:
        # 创建MQTT客户端
        client = mqtt.Client(client_id=MQTT_CLIENT_ID, clean_session=True)
        client.on_connect = on_connect
        client.on_publish = on_publish
        client.on_disconnect = on_disconnect
        
        print("1️⃣ 连接MQTT服务器...")
        client.connect(MQTT_BROKER_HOST, MQTT_BROKER_PORT, 60)
        client.loop_start()
        
        # 等待连接
        time.sleep(2)
        
        if mqtt_error.is_set():
            return False
        
        # 发送MQTT消息
        print("2️⃣ 发送MQTT康复信息...")
        test_data = create_test_data("mqtt")
        
        result = client.publish(
            MQTT_TOPIC,
            json.dumps(test_data, ensure_ascii=False),
            qos=1
        )
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print("📤 MQTT消息发布请求成功")
        else:
            print(f"❌ MQTT消息发布请求失败: {result.rc}")
            return False
        
        # 等待发布完成
        if mqtt_success.wait(timeout=5):
            print("✅ MQTT消息发布确认")
        else:
            print("⚠️ MQTT消息发布超时")
        
        # 清理
        client.loop_stop()
        client.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ MQTT协议测试异常: {e}")
        return False

def test_data_consistency():
    """测试数据一致性"""
    print("\n🔍 测试数据一致性")
    print("-" * 40)
    
    try:
        # 等待数据处理
        time.sleep(2)
        
        # 查询当前就诊信息
        response = requests.get(f"{HTTP_SERVER_URL}/api/current_visit", timeout=5)
        if response.status_code == 200:
            visit_info = response.json()
            print(f"✅ 最新就诊信息: {visit_info.get('patient_name', '')}")
            
            # 检查原始数据
            raw_data = visit_info.get('raw_data', {})
            source = raw_data.get('source', '')
            
            if 'mqtt' in source.lower():
                print("📡 最新数据来源: MQTT协议")
            elif 'http' in source.lower():
                print("🌐 最新数据来源: HTTP协议")
            else:
                print("❓ 数据来源未知")
            
            return True
        else:
            print(f"❌ 数据查询失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 数据一致性测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 HTTP/MQTT双协议综合测试")
    print("=" * 50)
    
    test_results = []
    
    # 测试HTTP协议
    http_result = test_http_protocol()
    test_results.append(("HTTP协议", http_result))
    
    # 等待一下
    time.sleep(1)
    
    # 测试MQTT协议
    mqtt_result = test_mqtt_protocol()
    test_results.append(("MQTT协议", mqtt_result))
    
    # 测试数据一致性
    consistency_result = test_data_consistency()
    test_results.append(("数据一致性", consistency_result))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("-" * 50)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("-" * 50)
    if all_passed:
        print("🎉 所有测试通过！双协议功能正常")
        print("💡 康复系统现在可以同时接收HTTP和MQTT数据")
    else:
        print("⚠️ 部分测试失败，请检查配置和服务状态")
    
    print("=" * 50)

if __name__ == '__main__':
    main()
