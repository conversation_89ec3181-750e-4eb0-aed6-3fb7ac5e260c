# buildAll.ps1 更新说明 v2.1.0

## 📋 更新概述

已成功更新 `buildAll.ps1` PowerShell构建脚本，整合了最新的HTTP/MQTT双协议服务器功能，支持完整的康复系统构建流程。

## ✨ 新增功能

### 1. 双协议服务器支持
- **HTTP协议**: 兼容现有前端系统
- **MQTT协议**: 符合平台标准v2.0规范
- **双版本构建**: 有界面和无界面两种版本

### 2. 音视频编码映射
- **137个文件**: 完整的编码到文件名映射表
- **多格式支持**: 单个编码、编码列表、媒体列表
- **自动处理**: 平台下发编码自动转换

### 3. 智能构建选项
- **灵活模式**: 可选择构建有界面/无界面/双版本
- **环境检查**: 自动检查Python、Node.js和必要文件
- **测试功能**: 内置编码映射测试

### 4. 完整工具链
- **启动脚本**: 自动生成有界面和无界面启动脚本
- **配置文件**: 包含主配置和MQTT配置示例
- **测试工具**: 编码映射测试脚本
- **说明文档**: 详细的使用说明

## 🔧 新增参数

### 构建模式参数
```powershell
-OnlyConsole        # 只构建有界面版本服务器
-OnlyNoConsole      # 只构建无界面版本服务器
-TestCodeMapping    # 测试音视频编码映射功能
```

### 原有参数
```powershell
-SkipHttpServer     # 跳过HTTP/MQTT服务器构建
-SkipElectron       # 跳过Electron前端构建
-Help               # 显示帮助信息
```

## 🚀 使用方法

### 1. 完整构建（推荐）
```powershell
.\buildAll.ps1
```
构建有界面+无界面双版本服务器 + Electron前端

### 2. 只构建服务器
```powershell
.\buildAll.ps1 -SkipElectron
```

### 3. 只构建有界面版本
```powershell
.\buildAll.ps1 -OnlyConsole
```

### 4. 只构建无界面版本
```powershell
.\buildAll.ps1 -OnlyNoConsole
```

### 5. 测试编码映射
```powershell
.\buildAll.ps1 -TestCodeMapping
```

### 6. 显示帮助
```powershell
.\buildAll.ps1 -Help
```

## 📁 输出文件结构

构建完成后的文件结构：

```
项目根目录/
├── dist-server/                          # 服务器输出目录
│   ├── httpServer.exe                     # 有界面版本服务器
│   ├── RehabilitationServer.exe           # 无界面版本服务器
│   ├── config.json                        # 主配置文件
│   ├── config-mqtt-example.json           # MQTT配置示例
│   ├── test_media_code_mapping.py         # 编码映射测试工具
│   ├── 启动服务器(有界面).bat             # 有界面启动脚本
│   ├── 启动服务器(无界面).bat             # 无界面启动脚本
│   ├── 测试编码映射.bat                   # 测试脚本
│   └── README.txt                         # 详细说明文档
└── dist-installer/                        # 前端输出目录
    └── *.exe                              # Electron安装包
```

## 🔍 构建流程

### 1. 环境检查
- ✅ Python版本检查
- ✅ Node.js版本检查
- ✅ 必要文件存在性检查

### 2. 服务器构建
- 🔨 执行 `build_server.py`
- 📦 生成有界面/无界面版本
- 📋 复制配置文件和工具
- 📝 生成启动脚本和文档

### 3. 前端构建
- 🎨 执行 `npm run build-win`
- 📦 生成Electron安装包
- 📁 检查输出目录

### 4. 结果汇总
- 📊 显示所有生成的文件
- ⏱️ 显示构建耗时
- 🎯 总结功能特性

## 📊 构建结果示例

```
🎉 [COMPLETE] 构建完成！
📊 构建结果: 2/2 成功

🖥️ HTTP/MQTT双协议服务器:
   ✅ 有界面版本: httpServer.exe (45.2 MB)
   ✅ 无界面版本: RehabilitationServer.exe (45.1 MB)

⚙️ 配置文件和工具:
   ✅ 主配置文件: config.json
   ✅ MQTT配置示例: config-mqtt-example.json
   ✅ 详细说明文档: README.txt
   ✅ 有界面启动脚本: 启动服务器(有界面).bat
   ✅ 无界面启动脚本: 启动服务器(无界面).bat
   ✅ 编码映射测试: 测试编码映射.bat

🎨 Electron前端安装包:
   ✅ 前端安装包: 心理康复系统 Setup 1.0.0.exe (89.5 MB)

🚀 功能特性:
   ✅ HTTP/MQTT双协议支持
   ✅ 音视频编码自动映射 (137个文件)
   ✅ 有界面/无界面双版本服务器
   ✅ 智能日志系统
   ✅ 完整的前端界面
   ✅ 符合平台MQTT协议v2.0标准

📁 输出目录: dist-server/ (服务器), dist-installer/ (前端)
⏱️ 总耗时: 03:45
```

## 🎯 核心改进

### 1. 智能化构建
- **自动环境检查**: 确保构建环境完整
- **灵活构建选项**: 支持多种构建模式
- **错误处理**: 详细的错误信息和恢复建议

### 2. 完整工具链
- **一键构建**: 单个脚本完成所有任务
- **配置管理**: 自动生成和复制配置文件
- **测试集成**: 内置编码映射测试功能

### 3. 用户友好
- **彩色输出**: 清晰的状态指示
- **详细帮助**: 完整的使用说明
- **进度显示**: 实时构建进度反馈

### 4. 标准化输出
- **统一结构**: 标准化的输出目录结构
- **完整文档**: 自动生成的说明文档
- **启动脚本**: 便于使用的启动脚本

## 📈 版本对比

| 功能 | v1.0 | v2.1.0 |
|------|------|--------|
| HTTP协议 | ✅ | ✅ |
| MQTT协议 | ❌ | ✅ |
| 编码映射 | ❌ | ✅ |
| 双版本构建 | ❌ | ✅ |
| 环境检查 | ❌ | ✅ |
| 测试功能 | ❌ | ✅ |
| 配置管理 | 基础 | 完整 |
| 文档生成 | 简单 | 详细 |

## 🎉 总结

更新后的 `buildAll.ps1` 脚本现在是一个功能完整的构建工具，能够：

✅ **一键构建**: 完整的康复系统构建流程  
✅ **双协议支持**: HTTP/MQTT双协议服务器  
✅ **灵活选择**: 多种构建模式可选  
✅ **智能检查**: 自动环境和依赖检查  
✅ **完整工具**: 包含测试和配置工具  
✅ **用户友好**: 清晰的输出和帮助信息  

现在您可以使用这个脚本轻松构建出完整的康复系统，包括服务器和前端！

---

**更新时间**: 2024-06-16  
**版本**: v2.1.0  
**兼容性**: 完全向后兼容  
**新增功能**: MQTT协议 + 编码映射 + 双版本构建
