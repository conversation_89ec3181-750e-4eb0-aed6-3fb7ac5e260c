# 视频文件目录

请将视频文件按分类放置在此目录下的子文件夹中。

## 目录结构示例

```
video/
├── 放松视频/
│   ├── 自然风景.mp4
│   ├── 海洋景观.avi
│   └── 森林漫步.mkv
├── 指导视频/
│   ├── 冥想指导.mp4
│   ├── 呼吸练习.webm
│   └── 放松技巧.mov
├── 教学视频/
│   ├── 心理健康.mp4
│   ├── 压力管理.avi
│   └── 情绪调节.wmv
└── 默认/
    ├── 测试视频.mp4
    └── 示例视频.avi
```

## 支持的视频格式

- MP4 (.mp4)
- AVI (.avi)
- MKV (.mkv)
- WebM (.webm)
- MOV (.mov)
- WMV (.wmv)
- FLV (.flv)

## 使用说明

1. **创建分类文件夹**：根据视频内容创建不同的子文件夹
2. **放置视频文件**：将相应的视频文件放入对应的分类文件夹
3. **刷新媒体库**：在播放器中点击"刷新媒体库"按钮
4. **选择分类**：在左侧目录树中点击分类名称查看文件
5. **播放视频**：点击文件列表右侧的播放按钮

## 注意事项

- 子文件夹名称将作为分类名称显示
- 直接放在 video 根目录下的文件会归入"默认"分类
- 支持多级子目录，会递归扫描所有视频文件
- 文件名建议使用中文或英文，避免特殊字符
- 视频播放支持全屏模式（F11键或点击全屏按钮）

## 推荐视频规格

- 分辨率：1920x1080 或 1280x720
- 编码：H.264
- 音频：AAC
- 容器：MP4（兼容性最好）
