('D:\\xinliceping\\Player\\dist-server\\RehabilitationServer.exe',
 <PERSON><PERSON><PERSON>,
 <PERSON><PERSON><PERSON>,
 <PERSON>alse,
 'C:\\Python39\\lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 None,
 <PERSON><PERSON><PERSON>,
 <PERSON><PERSON><PERSON>,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'D:\\xinliceping\\Player\\build-server\\rehabilitation_server_no_console\\RehabilitationServer.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\xinliceping\\Player\\build-server\\rehabilitation_server_no_console\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\xinliceping\\Player\\build-server\\rehabilitation_server_no_console\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\xinliceping\\Player\\build-server\\rehabilitation_server_no_console\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\xinliceping\\Player\\build-server\\rehabilitation_server_no_console\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\xinliceping\\Player\\build-server\\rehabilitation_server_no_console\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\xinliceping\\Player\\build-server\\rehabilitation_server_no_console\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Python39\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Python39\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Python39\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('http_server', 'D:\\xinliceping\\Player\\http_server.py', 'PYSOURCE'),
  ('python39.dll', 'C:\\Python39\\python39.dll', 'BINARY'),
  ('_decimal.pyd', 'C:\\Python39\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python39\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python39\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python39\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python39\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python39\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python39\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python39\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python39\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('simplejson\\_speedups.cp39-win_amd64.pyd',
   'C:\\Python39\\lib\\site-packages\\simplejson\\_speedups.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Python39\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_cffi_backend.cp39-win_amd64.pyd',
   'C:\\Python39\\lib\\site-packages\\_cffi_backend.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'C:\\Python39\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp39-win_amd64.pyd',
   'C:\\Python39\\lib\\site-packages\\charset_normalizer\\md.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'C:\\Python39\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll', 'C:\\php\\libcrypto-3-x64.dll', 'BINARY'),
  ('libssl-3-x64.dll', 'C:\\php\\libssl-3-x64.dll', 'BINARY'),
  ('python3.dll', 'C:\\Python39\\python3.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Python39\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Python39\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Python39\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'C:\\Python39\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Python39\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Python39\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'C:\\Python39\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'C:\\Python39\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Python39\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Python39\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('config-mqtt-example.json',
   'D:\\xinliceping\\Player\\config-mqtt-example.json',
   'DATA'),
  ('config.json', 'D:\\xinliceping\\Player\\config.json', 'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Python39\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Python39\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE',
   'C:\\Python39\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\INSTALLER',
   'C:\\Python39\\lib\\site-packages\\cryptography-41.0.7.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\METADATA',
   'C:\\Python39\\lib\\site-packages\\cryptography-41.0.7.dist-info\\METADATA',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\RECORD',
   'C:\\Python39\\lib\\site-packages\\cryptography-41.0.7.dist-info\\RECORD',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\WHEEL',
   'C:\\Python39\\lib\\site-packages\\cryptography-41.0.7.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\top_level.txt',
   'C:\\Python39\\lib\\site-packages\\cryptography-41.0.7.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE.APACHE',
   'C:\\Python39\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE.BSD',
   'C:\\Python39\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE.BSD',
   'DATA'),
  ('base_library.zip',
   'D:\\xinliceping\\Player\\build-server\\rehabilitation_server_no_console\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('runw.exe',
   'C:\\Python39\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Python39\\python39.dll')
