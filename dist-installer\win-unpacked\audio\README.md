# 音频文件目录

请将音频文件按分类放置在此目录下的子文件夹中。

## 目录结构示例

```
audio/
├── 轻音乐/
│   ├── 钢琴曲.mp3
│   ├── 小提琴曲.wav
│   └── 古典音乐.flac
├── 自然音效/
│   ├── 雨声.mp3
│   ├── 海浪声.wav
│   └── 鸟鸣声.ogg
├── 冥想音乐/
│   ├── 深度放松.mp3
│   ├── 正念冥想.wav
│   └── 瑜伽音乐.aac
└── 默认/
    ├── 背景音乐.mp3
    └── 测试音频.wav
```

## 支持的音频格式

- MP3 (.mp3)
- WAV (.wav)
- FLAC (.flac)
- AAC (.aac)
- OGG (.ogg)
- M4A (.m4a)

## 使用说明

1. **创建分类文件夹**：根据音频内容创建不同的子文件夹
2. **放置音频文件**：将相应的音频文件放入对应的分类文件夹
3. **刷新媒体库**：在播放器中点击"刷新媒体库"按钮
4. **选择分类**：在左侧目录树中点击分类名称查看文件
5. **播放音频**：点击文件列表右侧的播放按钮

## 注意事项

- 子文件夹名称将作为分类名称显示
- 直接放在 audio 根目录下的文件会归入"默认"分类
- 支持多级子目录，会递归扫描所有音频文件
- 文件名建议使用中文或英文，避免特殊字符
