2025-06-16 09:39:48,314 - INFO - 日志文件: logs\2025-06-16\log.txt
2025-06-16 09:39:48,320 - INFO - 已清理过期日志目录: 2025-06-09
2025-06-16 09:39:48,321 - INFO - === 康复系统配置信息 ===
2025-06-16 09:39:48,321 - INFO - 平台服务器: http://*************
2025-06-16 09:39:48,322 - INFO - 产品ID: rehabilitation_system
2025-06-16 09:39:48,322 - INFO - 设备ID: device_001
2025-06-16 09:39:48,322 - INFO - 认证Token: 1859128349437280256...
2025-06-16 09:39:48,322 - INFO - 请求超时: 10.0秒
2025-06-16 09:39:48,323 - INFO - === 平台API端点格式 ===
2025-06-16 09:39:48,323 - INFO - 心跳接口: http://*************/rehabilitation_system/device_001/heartbeat
2025-06-16 09:39:48,323 - INFO - 事件上报: http://*************/rehabilitation_system/device_001/event/{event_id}
2025-06-16 09:39:48,324 - INFO - 属性上报: http://*************/rehabilitation_system/device_001/properties/report
2025-06-16 09:39:48,324 - INFO - 统计上报: http://*************/rehabilitation_system/device_001/statistics
2025-06-16 09:39:48,324 - INFO - === MQTT配置信息 ===
2025-06-16 09:39:48,324 - INFO - MQTT状态: 已启用
2025-06-16 09:39:48,324 - INFO - MQTT服务器: *************:1883
2025-06-16 09:39:48,325 - INFO - 客户端ID: rehabilitation_device_001
2025-06-16 09:39:48,325 - INFO - 订阅主题: {'receive_rehabilitation_info': 'rehabilitation/device_001/receive_info', 'report_statistics': 'rehabilitation/device_001/report_stats', 'heartbeat': 'rehabilitation/device_001/heartbeat'}
2025-06-16 09:39:48,325 - INFO - ========================
2025-06-16 09:39:48,325 - INFO - 正在启动MQTT客户端...
2025-06-16 09:39:48,326 - INFO - 正在连接MQTT服务器: *************:1883
2025-06-16 09:39:53,334 - ERROR - MQTT连接异常: timed out
2025-06-16 09:39:53,336 - WARNING - MQTT客户端启动失败，仅使用HTTP协议
2025-06-16 09:39:53,340 - INFO - 发送心跳请求 - 完整URL: http://*************/rehabilitation_system/device_001/heartbeat
2025-06-16 09:39:53,340 - INFO - 心跳线程已启动
2025-06-16 09:39:53,341 - INFO - 请求方法: POST, 端点: /rehabilitation_system/device_001/heartbeat
2025-06-16 09:39:53,341 - INFO - 请求头: {'Authorization': 'Bearer 1859128349437280256', 'Content-Type': 'application/json'}
2025-06-16 09:39:53,341 - INFO - 请求体: {'timestamp': '2025-06-16T09:39:53.341687'}
2025-06-16 09:39:54,655 - INFO - 康复系统HTTP服务器启动成功
2025-06-16 09:39:54,655 - INFO - 监听地址: http://0.0.0.0:8888
2025-06-16 09:39:54,656 - INFO - === 支持的协议和接口 ===
2025-06-16 09:39:54,656 - INFO - HTTP API接口:
2025-06-16 09:39:54,656 - INFO -   POST /api/receive_rehabilitation_info - 接收康复信息
2025-06-16 09:39:54,656 - INFO -   POST /api/report_play_statistics - 上报播放统计
2025-06-16 09:39:54,656 - INFO -   GET  /api/status - 服务器状态
2025-06-16 09:39:54,656 - INFO -   GET  /api/current_visit - 当前就诊信息
2025-06-16 09:39:54,658 - INFO - MQTT订阅主题:
2025-06-16 09:39:54,658 - INFO -   receive_rehabilitation_info: rehabilitation/device_001/receive_info
2025-06-16 09:39:54,658 - INFO -   report_statistics: rehabilitation/device_001/report_stats
2025-06-16 09:39:54,658 - INFO -   heartbeat: rehabilitation/device_001/heartbeat
2025-06-16 09:39:54,659 - INFO - ========================
2025-06-16 09:39:54,659 - INFO - 服务器已启动，同时支持HTTP和MQTT协议接收数据
2025-06-16 09:40:03,357 - ERROR - 心跳发送异常: HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: /rehabilitation_system/device_001/heartbeat (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000293AA187C40>, 'Connection to ************* timed out. (connect timeout=10.0)'))
2025-06-16 09:40:03,358 - ERROR - 请求URL: http://*************/rehabilitation_system/device_001/heartbeat
2025-06-16 09:40:06,387 - INFO - 服务器正在关闭...
2025-06-16 09:40:06,387 - INFO - MQTT连接已断开
2025-06-16 10:56:54,862 - INFO - 日志文件: logs\2025-06-16\log.txt
2025-06-16 10:56:54,867 - INFO - === 康复系统配置信息 ===
2025-06-16 10:56:54,868 - INFO - 平台服务器: http://*************
2025-06-16 10:56:54,868 - INFO - 产品ID: rehabilitation_system
2025-06-16 10:56:54,868 - INFO - 设备ID: device_001
2025-06-16 10:56:54,869 - INFO - 认证Token: 1859128349437280256...
2025-06-16 10:56:54,869 - INFO - 请求超时: 10.0秒
2025-06-16 10:56:54,869 - INFO - === 平台API端点格式 ===
2025-06-16 10:56:54,869 - INFO - 心跳接口: http://*************/rehabilitation_system/device_001/heartbeat
2025-06-16 10:56:54,869 - INFO - 事件上报: http://*************/rehabilitation_system/device_001/event/{event_id}
2025-06-16 10:56:54,870 - INFO - 属性上报: http://*************/rehabilitation_system/device_001/properties/report
2025-06-16 10:56:54,870 - INFO - 统计上报: http://*************/rehabilitation_system/device_001/statistics
2025-06-16 10:56:54,870 - INFO - === MQTT配置信息 ===
2025-06-16 10:56:54,871 - INFO - MQTT状态: 已启用
2025-06-16 10:56:54,871 - INFO - MQTT服务器: *************:1883
2025-06-16 10:56:54,871 - INFO - 客户端ID: rehabilitation_device_001
2025-06-16 10:56:54,871 - INFO - 订阅主题: {'properties_read': '/{productId}/{deviceId}/properties/read', 'properties_write': '/{productId}/{deviceId}/properties/write', 'function_invoke': '/{productId}/{deviceId}/function/invoke', 'properties_report': '/{productId}/{deviceId}/properties/report', 'properties_report_reply': '/{productId}/{deviceId}/properties/report/reply', 'event_report': '/{productId}/{deviceId}/event/{eventId}', 'event_report_reply': '/{productId}/{deviceId}/event/{eventId}/reply'}
2025-06-16 10:56:54,871 - INFO - ========================
2025-06-16 10:56:54,872 - INFO - 正在启动MQTT客户端...
2025-06-16 10:56:54,874 - INFO - MQTT认证: username=rehabilitation_device_001|1750042614874
2025-06-16 10:56:54,874 - INFO - 正在连接MQTT服务器: *************:1883
2025-06-16 10:56:59,884 - ERROR - MQTT连接异常: timed out
2025-06-16 10:56:59,885 - WARNING - MQTT客户端启动失败，仅使用HTTP协议
2025-06-16 10:56:59,887 - INFO - 发送心跳请求 - 完整URL: http://*************/rehabilitation_system/device_001/heartbeat
2025-06-16 10:56:59,887 - INFO - 心跳线程已启动
2025-06-16 10:56:59,888 - INFO - 请求方法: POST, 端点: /rehabilitation_system/device_001/heartbeat
2025-06-16 10:56:59,888 - INFO - 请求头: {'Authorization': 'Bearer 1859128349437280256', 'Content-Type': 'application/json'}
2025-06-16 10:56:59,888 - INFO - 请求体: {'timestamp': '2025-06-16T10:56:59.888842'}
2025-06-16 10:57:01,282 - INFO - 康复系统HTTP服务器启动成功
2025-06-16 10:57:01,283 - INFO - 监听地址: http://0.0.0.0:8888
2025-06-16 10:57:01,283 - INFO - === 支持的协议和接口 ===
2025-06-16 10:57:01,283 - INFO - HTTP API接口:
2025-06-16 10:57:01,284 - INFO -   POST /api/receive_rehabilitation_info - 接收康复信息
2025-06-16 10:57:01,284 - INFO -   POST /api/report_play_statistics - 上报播放统计
2025-06-16 10:57:01,284 - INFO -   GET  /api/status - 服务器状态
2025-06-16 10:57:01,284 - INFO -   GET  /api/current_visit - 当前就诊信息
2025-06-16 10:57:01,284 - INFO - MQTT订阅主题:
2025-06-16 10:57:01,285 - INFO -   properties_read: /{productId}/{deviceId}/properties/read
2025-06-16 10:57:01,285 - INFO -   properties_write: /{productId}/{deviceId}/properties/write
2025-06-16 10:57:01,285 - INFO -   function_invoke: /{productId}/{deviceId}/function/invoke
2025-06-16 10:57:01,285 - INFO -   properties_report: /{productId}/{deviceId}/properties/report
2025-06-16 10:57:01,285 - INFO -   properties_report_reply: /{productId}/{deviceId}/properties/report/reply
2025-06-16 10:57:01,286 - INFO -   event_report: /{productId}/{deviceId}/event/{eventId}
2025-06-16 10:57:01,286 - INFO -   event_report_reply: /{productId}/{deviceId}/event/{eventId}/reply
2025-06-16 10:57:01,286 - INFO - ========================
2025-06-16 10:57:01,286 - INFO - 服务器已启动，同时支持HTTP和MQTT协议接收数据
2025-06-16 10:57:09,900 - ERROR - 心跳发送异常: HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: /rehabilitation_system/device_001/heartbeat (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x0000011A566BA670>, 'Connection to ************* timed out. (connect timeout=10.0)'))
2025-06-16 10:57:09,901 - ERROR - 请求URL: http://*************/rehabilitation_system/device_001/heartbeat
2025-06-16 10:57:21,232 - INFO - 服务器正在关闭...
2025-06-16 10:57:21,232 - INFO - MQTT连接已断开
