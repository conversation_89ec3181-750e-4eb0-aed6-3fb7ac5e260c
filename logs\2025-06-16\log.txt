2025-06-16 09:39:48,314 - INFO - 日志文件: logs\2025-06-16\log.txt
2025-06-16 09:39:48,320 - INFO - 已清理过期日志目录: 2025-06-09
2025-06-16 09:39:48,321 - INFO - === 康复系统配置信息 ===
2025-06-16 09:39:48,321 - INFO - 平台服务器: http://*************
2025-06-16 09:39:48,322 - INFO - 产品ID: rehabilitation_system
2025-06-16 09:39:48,322 - INFO - 设备ID: device_001
2025-06-16 09:39:48,322 - INFO - 认证Token: 1859128349437280256...
2025-06-16 09:39:48,322 - INFO - 请求超时: 10.0秒
2025-06-16 09:39:48,323 - INFO - === 平台API端点格式 ===
2025-06-16 09:39:48,323 - INFO - 心跳接口: http://*************/rehabilitation_system/device_001/heartbeat
2025-06-16 09:39:48,323 - INFO - 事件上报: http://*************/rehabilitation_system/device_001/event/{event_id}
2025-06-16 09:39:48,324 - INFO - 属性上报: http://*************/rehabilitation_system/device_001/properties/report
2025-06-16 09:39:48,324 - INFO - 统计上报: http://*************/rehabilitation_system/device_001/statistics
2025-06-16 09:39:48,324 - INFO - === MQTT配置信息 ===
2025-06-16 09:39:48,324 - INFO - MQTT状态: 已启用
2025-06-16 09:39:48,324 - INFO - MQTT服务器: *************:1883
2025-06-16 09:39:48,325 - INFO - 客户端ID: rehabilitation_device_001
2025-06-16 09:39:48,325 - INFO - 订阅主题: {'receive_rehabilitation_info': 'rehabilitation/device_001/receive_info', 'report_statistics': 'rehabilitation/device_001/report_stats', 'heartbeat': 'rehabilitation/device_001/heartbeat'}
2025-06-16 09:39:48,325 - INFO - ========================
2025-06-16 09:39:48,325 - INFO - 正在启动MQTT客户端...
2025-06-16 09:39:48,326 - INFO - 正在连接MQTT服务器: *************:1883
2025-06-16 09:39:53,334 - ERROR - MQTT连接异常: timed out
2025-06-16 09:39:53,336 - WARNING - MQTT客户端启动失败，仅使用HTTP协议
2025-06-16 09:39:53,340 - INFO - 发送心跳请求 - 完整URL: http://*************/rehabilitation_system/device_001/heartbeat
2025-06-16 09:39:53,340 - INFO - 心跳线程已启动
2025-06-16 09:39:53,341 - INFO - 请求方法: POST, 端点: /rehabilitation_system/device_001/heartbeat
2025-06-16 09:39:53,341 - INFO - 请求头: {'Authorization': 'Bearer 1859128349437280256', 'Content-Type': 'application/json'}
2025-06-16 09:39:53,341 - INFO - 请求体: {'timestamp': '2025-06-16T09:39:53.341687'}
2025-06-16 09:39:54,655 - INFO - 康复系统HTTP服务器启动成功
2025-06-16 09:39:54,655 - INFO - 监听地址: http://0.0.0.0:8888
2025-06-16 09:39:54,656 - INFO - === 支持的协议和接口 ===
2025-06-16 09:39:54,656 - INFO - HTTP API接口:
2025-06-16 09:39:54,656 - INFO -   POST /api/receive_rehabilitation_info - 接收康复信息
2025-06-16 09:39:54,656 - INFO -   POST /api/report_play_statistics - 上报播放统计
2025-06-16 09:39:54,656 - INFO -   GET  /api/status - 服务器状态
2025-06-16 09:39:54,656 - INFO -   GET  /api/current_visit - 当前就诊信息
2025-06-16 09:39:54,658 - INFO - MQTT订阅主题:
2025-06-16 09:39:54,658 - INFO -   receive_rehabilitation_info: rehabilitation/device_001/receive_info
2025-06-16 09:39:54,658 - INFO -   report_statistics: rehabilitation/device_001/report_stats
2025-06-16 09:39:54,658 - INFO -   heartbeat: rehabilitation/device_001/heartbeat
2025-06-16 09:39:54,659 - INFO - ========================
2025-06-16 09:39:54,659 - INFO - 服务器已启动，同时支持HTTP和MQTT协议接收数据
2025-06-16 09:40:03,357 - ERROR - 心跳发送异常: HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: /rehabilitation_system/device_001/heartbeat (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000293AA187C40>, 'Connection to ************* timed out. (connect timeout=10.0)'))
2025-06-16 09:40:03,358 - ERROR - 请求URL: http://*************/rehabilitation_system/device_001/heartbeat
2025-06-16 09:40:06,387 - INFO - 服务器正在关闭...
2025-06-16 09:40:06,387 - INFO - MQTT连接已断开
