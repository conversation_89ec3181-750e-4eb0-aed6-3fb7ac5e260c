#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT客户端测试脚本
用于测试康复系统的MQTT功能
"""

import json
import time
from datetime import datetime

# MQTT支持
try:
    import paho.mqtt.client as mqtt
    MQTT_AVAILABLE = True
    print("[INFO] MQTT库已加载")
except ImportError:
    MQTT_AVAILABLE = False
    print("[ERROR] MQTT库未安装，请运行: pip install paho-mqtt")
    exit(1)

# 配置信息
MQTT_BROKER_HOST = "*************"
MQTT_BROKER_PORT = 1883
MQTT_CLIENT_ID = "test_mqtt_client"
MQTT_TOPIC = "rehabilitation/device_001/receive_info"

def on_connect(client, userdata, flags, rc):
    """连接回调"""
    if rc == 0:
        print(f"✅ MQTT连接成功: {MQTT_BROKER_HOST}:{MQTT_BROKER_PORT}")
    else:
        print(f"❌ MQTT连接失败，错误码: {rc}")

def on_publish(client, userdata, mid):
    """发布回调"""
    print(f"✅ 消息发布成功，消息ID: {mid}")

def on_disconnect(client, userdata, rc):
    """断开连接回调"""
    print(f"🔌 MQTT连接断开，错误码: {rc}")

def create_test_data():
    """创建测试数据"""
    return {
        "company": "测试医院",
        "assessment": "音乐视频疗法量表",
        "basic_info": {
            "id": "20240616001",
            "department": "心理康复科",
            "name": "李四",
            "gender": "女",
            "age": 28
        },
        "training_task": {
            "training_project": "MQTT轻音乐放松训练",
            "training_duration": 25,
            "training_frequency": 3
        },
        "training_effect": {
            "completed_project": "",
            "completed_duration": 0,
            "completed_frequency": 0
        },
        "signature": "王医生",
        "report_date": datetime.now().strftime("%Y-%m-%d"),
        "source": "mqtt_test"
    }

def test_mqtt_publish():
    """测试MQTT发布"""
    try:
        # 创建MQTT客户端
        client = mqtt.Client(client_id=MQTT_CLIENT_ID, clean_session=True)
        
        # 设置回调函数
        client.on_connect = on_connect
        client.on_publish = on_publish
        client.on_disconnect = on_disconnect
        
        print(f"🔄 正在连接MQTT服务器: {MQTT_BROKER_HOST}:{MQTT_BROKER_PORT}")
        
        # 连接到MQTT服务器
        client.connect(MQTT_BROKER_HOST, MQTT_BROKER_PORT, 60)
        
        # 启动网络循环
        client.loop_start()
        
        # 等待连接建立
        time.sleep(2)
        
        # 创建测试数据
        test_data = create_test_data()
        
        print("📋 发送的测试数据:")
        print(json.dumps(test_data, ensure_ascii=False, indent=2))
        
        # 发布消息
        print(f"📤 发布消息到主题: {MQTT_TOPIC}")
        result = client.publish(
            MQTT_TOPIC,
            json.dumps(test_data, ensure_ascii=False),
            qos=1
        )
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print("✅ 消息发布请求成功")
        else:
            print(f"❌ 消息发布请求失败: {result.rc}")
        
        # 等待消息发布完成
        time.sleep(3)
        
        # 断开连接
        client.loop_stop()
        client.disconnect()
        
        print("🎉 MQTT测试完成")
        return True
        
    except Exception as e:
        print(f"❌ MQTT测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 MQTT客户端测试工具")
    print("=" * 50)
    
    if not MQTT_AVAILABLE:
        print("❌ MQTT库未安装，无法进行测试")
        return
    
    print(f"📡 目标服务器: {MQTT_BROKER_HOST}:{MQTT_BROKER_PORT}")
    print(f"📝 目标主题: {MQTT_TOPIC}")
    print(f"🆔 客户端ID: {MQTT_CLIENT_ID}")
    print("=" * 50)
    
    # 执行测试
    success = test_mqtt_publish()
    
    if success:
        print("\n✅ 所有测试通过")
        print("💡 提示: 请检查康复系统HTTP服务器日志，确认是否收到MQTT消息")
    else:
        print("\n❌ 测试失败")
        print("💡 提示: 请检查MQTT服务器是否运行，网络连接是否正常")

if __name__ == '__main__':
    main()
