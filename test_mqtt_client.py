#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT客户端测试脚本 - 符合平台标准协议
用于测试康复系统的MQTT功能
"""

import json
import time
import hashlib
from datetime import datetime

# MQTT支持
try:
    import paho.mqtt.client as mqtt
    MQTT_AVAILABLE = True
    print("[INFO] MQTT库已加载")
except ImportError:
    MQTT_AVAILABLE = False
    print("[ERROR] MQTT库未安装，请运行: pip install paho-mqtt")
    exit(1)

# 配置信息 - 符合平台标准
MQTT_BROKER_HOST = "*************"
MQTT_BROKER_PORT = 1883
MQTT_CLIENT_ID = "test_platform_client"
PRODUCT_ID = "rehabilitation_system"
DEVICE_ID = "device_001"
SECURE_ID = "test_platform"
SECURE_KEY = "test_secure_key"

def generate_auth_credentials():
    """生成MQTT认证凭据"""
    timestamp = str(int(time.time() * 1000))  # 毫秒时间戳
    username = f"{SECURE_ID}|{timestamp}"

    # 生成password: md5(secureId + "|" + timestamp + "|" + secureKey)
    password_str = f"{SECURE_ID}|{timestamp}|{SECURE_KEY}"
    password = hashlib.md5(password_str.encode('utf-8')).hexdigest()

    return username, password

def build_topic(topic_template, event_id=None):
    """构建标准MQTT主题"""
    topic = topic_template.replace('{productId}', PRODUCT_ID)
    topic = topic.replace('{deviceId}', DEVICE_ID)
    if event_id:
        topic = topic.replace('{eventId}', event_id)
    return topic

def on_connect(client, userdata, flags, rc):
    """连接回调"""
    if rc == 0:
        print(f"✅ MQTT连接成功: {MQTT_BROKER_HOST}:{MQTT_BROKER_PORT}")
    else:
        print(f"❌ MQTT连接失败，错误码: {rc}")

def on_publish(client, userdata, mid):
    """发布回调"""
    print(f"✅ 消息发布成功，消息ID: {mid}")

def on_disconnect(client, userdata, rc):
    """断开连接回调"""
    print(f"🔌 MQTT连接断开，错误码: {rc}")

def create_function_invoke_data():
    """创建功能调用测试数据"""
    rehabilitation_info = {
        "company": "测试医院",
        "assessment": "音乐视频疗法量表",
        "basic_info": {
            "id": "20240616001",
            "department": "心理康复科",
            "name": "李四",
            "gender": "女",
            "age": 28
        },
        "training_task": {
            "training_project": "MQTT轻音乐放松训练",
            "training_duration": 25,
            "training_frequency": 3
        },
        "training_effect": {
            "completed_project": "",
            "completed_duration": 0,
            "completed_frequency": 0
        },
        "signature": "王医生",
        "report_date": datetime.now().strftime("%Y-%m-%d"),
        "source": "mqtt_platform_test"
    }

    return {
        "timestamp": int(time.time() * 1000),
        "messageId": f"test_msg_{int(time.time() * 1000)}",
        "deviceId": DEVICE_ID,
        "functionId": "start_rehabilitation",
        "headers": {
            "visitId": "43a962b625eb4025a7d70a39a5bf6a2b",
            "patientId": "3586d330b4df4e8b8a9cf43bbda0b6c7",
            "visitNo": "202401010001",
            "itemId": "69bfcd0184cb464eb5c2aed22eb653a4",
            "itemType": "1"
        },
        "inputs": [
            {
                "name": "rehabilitation_info",
                "value": json.dumps(rehabilitation_info, ensure_ascii=False)
            }
        ]
    }

def create_properties_read_data():
    """创建属性读取测试数据"""
    return {
        "timestamp": int(time.time() * 1000),
        "messageId": f"read_msg_{int(time.time() * 1000)}",
        "deviceId": DEVICE_ID,
        "properties": ["sn", "model", "status", "version"]
    }

def test_function_invoke():
    """测试功能调用"""
    try:
        # 生成认证凭据
        username, password = generate_auth_credentials()

        # 创建MQTT客户端
        client = mqtt.Client(client_id=MQTT_CLIENT_ID, clean_session=True)

        # 设置认证
        client.username_pw_set(username, password)

        # 设置回调函数
        client.on_connect = on_connect
        client.on_publish = on_publish
        client.on_disconnect = on_disconnect

        print(f"🔄 正在连接MQTT服务器: {MQTT_BROKER_HOST}:{MQTT_BROKER_PORT}")
        print(f"🔐 认证信息: username={username}")

        # 连接到MQTT服务器
        client.connect(MQTT_BROKER_HOST, MQTT_BROKER_PORT, 60)

        # 启动网络循环
        client.loop_start()

        # 等待连接建立
        time.sleep(2)

        # 创建功能调用数据
        function_data = create_function_invoke_data()

        print("📋 发送的功能调用数据:")
        print(json.dumps(function_data, ensure_ascii=False, indent=2))

        # 构建功能调用主题
        function_topic = build_topic("/{productId}/{deviceId}/function/invoke")

        # 发布消息
        print(f"📤 发布消息到主题: {function_topic}")
        result = client.publish(
            function_topic,
            json.dumps(function_data, ensure_ascii=False),
            qos=1
        )

        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print("✅ 功能调用消息发布成功")
        else:
            print(f"❌ 功能调用消息发布失败: {result.rc}")

        # 等待消息发布完成
        time.sleep(3)

        # 断开连接
        client.loop_stop()
        client.disconnect()

        print("🎉 功能调用测试完成")
        return True

    except Exception as e:
        print(f"❌ 功能调用测试异常: {e}")
        return False

def test_properties_read():
    """测试属性读取"""
    try:
        # 生成认证凭据
        username, password = generate_auth_credentials()

        # 创建MQTT客户端
        client = mqtt.Client(client_id=f"{MQTT_CLIENT_ID}_read", clean_session=True)

        # 设置认证
        client.username_pw_set(username, password)

        # 设置回调函数
        client.on_connect = on_connect
        client.on_publish = on_publish
        client.on_disconnect = on_disconnect

        print(f"🔄 正在连接MQTT服务器进行属性读取测试...")

        # 连接到MQTT服务器
        client.connect(MQTT_BROKER_HOST, MQTT_BROKER_PORT, 60)

        # 启动网络循环
        client.loop_start()

        # 等待连接建立
        time.sleep(2)

        # 创建属性读取数据
        read_data = create_properties_read_data()

        print("📋 发送的属性读取数据:")
        print(json.dumps(read_data, ensure_ascii=False, indent=2))

        # 构建属性读取主题
        read_topic = build_topic("/{productId}/{deviceId}/properties/read")

        # 发布消息
        print(f"📤 发布消息到主题: {read_topic}")
        result = client.publish(
            read_topic,
            json.dumps(read_data, ensure_ascii=False),
            qos=1
        )

        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print("✅ 属性读取消息发布成功")
        else:
            print(f"❌ 属性读取消息发布失败: {result.rc}")

        # 等待消息发布完成
        time.sleep(3)

        # 断开连接
        client.loop_stop()
        client.disconnect()

        print("🎉 属性读取测试完成")
        return True

    except Exception as e:
        print(f"❌ 属性读取测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 MQTT平台标准协议测试工具")
    print("=" * 60)

    if not MQTT_AVAILABLE:
        print("❌ MQTT库未安装，无法进行测试")
        return

    print(f"📡 目标服务器: {MQTT_BROKER_HOST}:{MQTT_BROKER_PORT}")
    print(f"🏭 产品ID: {PRODUCT_ID}")
    print(f"📱 设备ID: {DEVICE_ID}")
    print(f"🆔 客户端ID: {MQTT_CLIENT_ID}")
    print(f"🔐 安全ID: {SECURE_ID}")
    print("=" * 60)

    # 执行测试
    tests = [
        ("功能调用测试", test_function_invoke),
        ("属性读取测试", test_properties_read)
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 开始 {test_name}...")
        print("-" * 40)
        success = test_func()
        results.append((test_name, success))
        print("-" * 40)
        time.sleep(2)  # 测试间隔

    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("-" * 60)

    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False

    print("-" * 60)
    if all_passed:
        print("🎉 所有测试通过！")
        print("💡 提示: 请检查康复系统HTTP服务器日志，确认是否收到MQTT消息")
        print("📋 测试内容:")
        print("   - 功能调用: 模拟平台下发康复训练指令")
        print("   - 属性读取: 模拟平台读取设备属性")
    else:
        print("⚠️ 部分测试失败，请检查:")
        print("   1. MQTT服务器是否运行")
        print("   2. 网络连接是否正常")
        print("   3. 认证信息是否正确")
        print("   4. 康复系统是否正常运行")

    print("=" * 60)

if __name__ == '__main__':
    main()
