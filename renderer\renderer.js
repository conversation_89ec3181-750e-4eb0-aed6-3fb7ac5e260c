const { ipc<PERSON>enderer } = require('electron');
const path = require('path');

// 播放器状态
let mediaLibrary = { audio: {}, video: {} };
let currentCategory = null;
let currentFiles = [];
let currentPage = 1;
let itemsPerPage = 20;
let totalPages = 1;
let currentPlayingFile = null;
let isPlaying = false;
let currentPlayer = null;
let playMode = 'sequence'; // sequence, repeat, random
let mediaRefreshInterval = null; // 媒体库自动刷新定时器

// 就诊信息和httpServer通信
let patientInfo = {
    name: '',
    visitNumber: '',
    mediaNames: [],
    sessionDuration: 0
};
let httpServerUrl = 'http://localhost:8888';
let config = null; // 配置文件
let patientInfoCheckInterval = null; // 就诊信息检查定时器
let playTimeTracker = null; // 播放时长统计定时器
let currentSessionStartTime = null; // 当前会话开始时间
let totalPlayTime = 0; // 总播放时长（秒）

// 任务管理
let taskList = []; // 任务列表
let currentTask = null; // 当前执行的任务
let taskFilePath = 'tasks.json'; // 任务文件路径

// 音视频编码映射表
const MEDIA_CODE_MAPPING = {
    "72539895": "1.止损负面情绪.mp3",
    "54765957": "2.抵御对方的伤害.mp3",
    "20941793": "3.避免误伤对方.mp3",
    "98876421": "4.专属于我的小屋.mp3",
    "87424978": "5.来自本我的安抚.mp3",
    "14687668": "课程介绍.mp3",
    "45577303": "1.你是爱和光.mp3",
    "49328392": "2.感恩拥有的.mp3",
    "25547785": "3.原谅自己.mp3",
    "40988847": "4.关注你想要的.mp3",
    "46360509": "5.未来照进现实.mp3",
    "35615503": "1.呼吸介绍.mp3",
    "44704557": "2.神圣呼吸.mp3",
    "47791818": "3.能量保护.mp3",
    "20651243": "4.能量平衡.mp3",
    "60509158": "5.重生之旅.mp3",
    "27584525": "6.破解焦虑密码.mp3",
    "58283184": "0，引导语.mp3",
    "31175349": "1.什么是活在当下.mp3",
    "62606174": "2.修复身心的能量.mp3",
    "17939535": "3.在生活工作中回到当下.mp3",
    "92771386": "4.从当下疗愈过去.mp3",
    "97320227": "5.从当下创造未来.mp3",
    "59638651": "6.开启内在的智慧.mp3",
    "52683424": "7，享受美好的当下.mp3",
    "15546523": "1.睡前准备.mp3",
    "83467213": "2.放松身体.mp3",
    "35347940": "3.无反应练习.mp3",
    "31702818": "4.消除焦虑.mp3",
    "22178461": "5.学会放下.mp3",
    "51742064": "6.良好睡眠的思考模式.mp3",
    "81720804": "7.开启良好睡眠.mp3",
    "94721479": "1.基础呼吸训练.mp3",
    "28428375": "2.品味美好.mp3",
    "57473904": "3.发现最好的自己.mp3",
    "66174432": "4.遇见未来的自己.mp3",
    "59545771": "5.点亮别人的日子.mp3",
    "87421929": "6.学会乐观.mp3",
    "73543793": "7.感恩幸运.mp3",
    "11466916": "8.停止忧虑.mp3",
    "37054560": "9.应对创伤.mp3",
    "28127421": "10.创造福流.mp3",
    "69985783": "爱我的灵魂.mp3",
    "12314605": "爱我的内心.mp3",
    "41070070": "爱我的身体.mp3",
    "35604236": "开启美好一天.mp3",
    "27913577": "清晨唤醒冥想.mp3",
    "10745888": "清晨心情冥想.mp3",
    "77055539": "等待.mp3",
    "51993412": "公交地铁.mp3",
    "19772591": "汽车.mp3",
    "48242031": "Alpha冥想.mp3",
    "80569795": "Alpha钟铃冥想.mp3",
    "29099482": "Theta冥想.mp3",
    "86541325": "Theta钟铃冥想.mp3",
    "35523589": "壁炉中的倒影.mp3",
    "31117915": "城市的雨天.mp3",
    "88901191": "春之歌.mp3",
    "15255614": "海风.mp3",
    "53062144": "来自森林的呼吸.mp3",
    "48860733": "雷雨夜.mp3",
    "67999585": "山溪与鸟鸣.mp3",
    "11379721": "夜啼.mp3",
    "63596857": "莺啼醒了清晨.mp3",
    "51509559": "竹风铃之境.mp3",
    "27753035": "鼻子的探索.mp3",
    "12461363": "肚皮上的小船.mp3",
    "35727535": "和一朵云一起呼吸.mp3",
    "84253686": "我对身体微笑.mp3",
    "64961346": "像小青蛙一样呼吸.mp3",
    "29088982": "正念品茶.mp3",
    "72084149": "专注石.mp3",
    "62140902": "放松静心-英文版.mp3",
    "14392224": "放松静心-中文版.mp3",
    "79866738": "乌帕迪英文原版采访.mp3",
    "23600381": "乌帕迪中文翻译采访.mp3",
    "12172400": "1.打败消极.mp3",
    "37855048": "3.平静的内心(吉他).mp3",
    "64749734": "4.和谐的独奏(钢琴).mp3",
    "41330176": "5.阳光下的天使.mp3",
    "23102933": "改善挫败冥想.mp3",
    "35242410": "工作间歇冥想.mp3",
    "70270178": "工作减压冥想.mp3",
    "35414419": "提升专注冥想.mp3",
    "57488224": "午休放松冥想.mp3",
    "44583787": "慈心.mp3",
    "33674999": "接纳.mp3",
    "66295094": "矛盾.mp3",
    "46615298": "视角.mp3",
    "73113182": "原谅.mp3",
    "31633619": "5分钟•缓解焦虑.mp3",
    "80808841": "15分钟•缓解焦虑.mp3",
    "70156874": "30分钟•缓解焦虑.mp3",
    "34405296": "5分钟•接纳自我.mp3",
    "96253561": "15分钟•接纳自我.mp3",
    "71919306": "30分钟•接纳自我.mp3",
    "18206307": "安住的境界.mp3",
    "57462855": "把心唤醒.mp3",
    "88720929": "1.静坐的基本指引.mp3",
    "39299957": "2.对声音的觉察.mp3",
    "69003325": "3.呼吸的力量.mp3",
    "87845536": "4.横膈膜呼吸法.mp3",
    "34691933": "5.心与身的关联.mp3",
    "59091983": "6.身心合一的解脱.mp3",
    "35152051": "摆脱沮丧冥想.mp3",
    "30508386": "改善抑郁冥想.mp3",
    "68169365": "缓解恐惧冥想.mp3",
    "74359751": "缓解疼痛冥想.mp3",
    "22632579": "消除紧张冥想.mp3",
    "84096201": "5分钟•身体扫描.mp3",
    "20647041": "15分钟•身体扫描.mp3",
    "96261357": "30分钟•身体扫描.mp3",
    "47249223": "考前放松冥想.mp3",
    "42627084": "课间休息冥想.mp3",
    "20290538": "课前提神冥想.mp3",
    "65820595": "爱的海洋.mp3",
    "17923956": "耳语笔记.mp3",
    "20466133": "平静的波涛.mp3",
    "25555860": "天堂的祈祷.mp3",
    "97161435": "我听到竖琴声.mp3",
    "41164391": "慈悲与宽恕.mp3",
    "32683996": "接纳无常.mp3",
    "29290004": "进入存在模式.mp3",
    "56050757": "内心的游戏.mp3",
    "19957292": "深度冥想.mp3",
    "87157414": "识别\"自动导航\".mp3",
    "96598773": "探索内在资源.mp3",
    "78111396": "压力的本能反应.mp3",
    "71645029": "压力应对-给情绪命名.mp3",
    "14651553": "压力应对-运用呼吸.mp3",
    "68380251": "正念的内涵和功效.mp3",
    "53740920": "正念的七种态度.mp3",
    "77224914": "抗挫力冥想练习.mp3",
    "80991454": "三分钟椅子冥想练习.mp3",
    "33890496": "十分钟身体扫描.mp3",
    "45587282": "同理心冥想练习.mp3",
    "44047606": "五分钟椅子冥想练习.mp3",
    "96240370": "自我照顾冥想练习.mp3",
    "33326899": "5分钟缓解焦虑.mp4",
    "51715039": "5分钟身体扫描.mp4",
    "47177327": "来自本我的安抚.mp4"
};

// 编码处理函数
function getMediaNameByCode(code) {
    return MEDIA_CODE_MAPPING[String(code)] || `未知编码_${code}`;
}

function getMediaCodeByName(name) {
    for (const [code, filename] of Object.entries(MEDIA_CODE_MAPPING)) {
        if (filename === name) {
            return code;
        }
    }
    return null;
}

// DOM 元素（将在DOMContentLoaded时初始化）
let elements = {};

// 安全获取DOM元素的辅助函数
function safeGetElement(id) {
    const element = document.getElementById(id);
    if (!element) {
        console.warn(`DOM元素未找到: ${id}`);
    }
    return element;
}

// 初始化DOM元素
function initializeElements() {
    elements = {
        // 就诊信息相关
        patientName: safeGetElement('patientName'),
        visitNumber: safeGetElement('visitNumber'),
        mediaNames: safeGetElement('mediaNames'),
        sessionDuration: safeGetElement('sessionDuration'),

        // 任务管理相关
        taskList: safeGetElement('taskList'),
        taskCount: safeGetElement('taskCount'),
        taskCompleteModal: safeGetElement('taskCompleteModal'),
        taskModalCloseBtn: safeGetElement('taskModalCloseBtn'),
        taskCompleteOkBtn: safeGetElement('taskCompleteOkBtn'),
        completeTaskName: safeGetElement('completeTaskName'),
        completeTaskDuration: safeGetElement('completeTaskDuration'),

        refreshBtn: safeGetElement('refreshBtn'),
        audioTabBtn: safeGetElement('audioTabBtn'),
        videoTabBtn: safeGetElement('videoTabBtn'),
        openMediaFolderBtn: safeGetElement('openMediaFolderBtn'),
        settingsBtn: safeGetElement('settingsBtn'),
        minimizeBtn: safeGetElement('minimizeBtn'),
        closeBtn: safeGetElement('closeBtn'),

        // 目录树
        audioCategories: safeGetElement('audioCategories'),
        videoCategories: safeGetElement('videoCategories'),
        audioCount: safeGetElement('audioCount'),
        videoCount: safeGetElement('videoCount'),
        emptyState: safeGetElement('emptyState'),

        // 文件列表
        currentPath: safeGetElement('currentPath'),
        fileCount: safeGetElement('fileCount'),
        playAllBtn: safeGetElement('playAllBtn'),
        fileList: safeGetElement('fileList'),

        // 分页
        pagination: safeGetElement('pagination'),
        prevPageBtn: safeGetElement('prevPageBtn'),
        nextPageBtn: safeGetElement('nextPageBtn'),
        pageInfo: safeGetElement('pageInfo'),
        videoContainer: safeGetElement('videoContainer'),
        audioContainer: safeGetElement('audioContainer'),
        videoPlayer: safeGetElement('videoPlayer'),
        audioPlayer: safeGetElement('audioPlayer'),
        playPauseBtn: safeGetElement('playPauseBtn'),
        prevBtn: safeGetElement('prevBtn'),
        nextBtn: safeGetElement('nextBtn'),
        stopBtn: safeGetElement('stopBtn'),
        progressSlider: safeGetElement('progressSlider'),
        volumeSlider: safeGetElement('volumeSlider'),
        muteBtn: safeGetElement('muteBtn'),
        fullscreenBtn: safeGetElement('fullscreenBtn'),
        videoCloseBtn: safeGetElement('videoCloseBtn'),
        currentTime: safeGetElement('currentTime'),
        totalTime: safeGetElement('totalTime'),
        trackTitle: safeGetElement('trackTitle'),
        trackArtist: safeGetElement('trackArtist'),
        statusText: safeGetElement('statusText'),
        playMode: safeGetElement('playMode'),
        playModeBtn: safeGetElement('playModeBtn'),
        contextMenu: safeGetElement('contextMenu'),

        // 播放列表相关（可能不存在）
        playlist: safeGetElement('playlist'),
        playlistCount: safeGetElement('playlistCount')
    };

    console.log('DOM元素初始化完成');
}

// 加载配置文件
async function loadConfig() {
    try {
        const configData = await ipcRenderer.invoke('load-config');
        config = configData;

        // 更新httpServer URL
        if (config && config.httpserver) {
            let host = config.httpserver.host || 'localhost';
            const port = config.httpserver.port || 8888;

            // 如果服务器监听0.0.0.0，前端应该连接localhost
            if (host === '0.0.0.0') {
                host = 'localhost';
            }

            httpServerUrl = `http://${host}:${port}`;
        }

        console.log('配置文件加载成功:', config);
        console.log('httpServer URL:', httpServerUrl);
        return true;
    } catch (error) {
        console.error('配置文件加载失败:', error);
        return false;
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOMContentLoaded事件触发');
    await loadConfig(); // 首先加载配置
    initializeElements();
    initializeEventListeners();
    await loadMediaLibrary();
    startMediaRefreshTimer();
    startPatientInfoChecker();
    updatePatientInfoDisplay();
    await loadTaskList();
    updateTaskDisplay();
    updateStatus('就绪');
});

// 加载媒体库
async function loadMediaLibrary() {
    try {
        updateStatus('正在扫描媒体文件...');
        console.log('🔄 开始加载媒体库...');
        const mediaData = await ipcRenderer.invoke('get-builtin-media');
        console.log('📦 收到媒体数据:', mediaData);
        console.log('  音频分类:', Object.keys(mediaData.audio));
        console.log('  视频分类:', Object.keys(mediaData.video));
        mediaLibrary = mediaData;

        updateDirectoryTree();
        console.log('✅ 媒体库加载完成，当前数据:', mediaLibrary);
        updateStatus('媒体库加载完成');
    } catch (error) {
        console.error('加载媒体库失败:', error);
        updateStatus('加载媒体库失败');
    }
}

// 更新目录树
function updateDirectoryTree() {
    updateAudioTree();
    updateVideoTree();

    // 检查是否有媒体文件
    const hasAudio = Object.keys(mediaLibrary.audio).length > 0;
    const hasVideo = Object.keys(mediaLibrary.video).length > 0;

    if (!hasAudio && !hasVideo) {
        elements.emptyState.style.display = 'block';
    } else {
        elements.emptyState.style.display = 'none';
    }
}

// 更新音频目录树
function updateAudioTree() {
    const audioData = mediaLibrary.audio;
    let totalCount = 0;

    elements.audioCategories.innerHTML = '';

    for (const [categoryName, files] of Object.entries(audioData)) {
        totalCount += files.length;

        const categoryNode = document.createElement('div');
        categoryNode.className = 'tree-node child-node';
        categoryNode.dataset.type = 'audio';
        categoryNode.dataset.category = categoryName;
        categoryNode.innerHTML = `
            <span class="node-icon">📁</span>
            <span class="node-label">${categoryName}</span>
            <span class="node-count">${files.length}</span>
        `;

        categoryNode.addEventListener('click', () => selectCategory('audio', categoryName));
        elements.audioCategories.appendChild(categoryNode);
    }

    elements.audioCount.textContent = totalCount;
}

// 更新视频目录树
function updateVideoTree() {
    const videoData = mediaLibrary.video;
    let totalCount = 0;

    console.log('🎬 更新视频目录树');
    console.log('  视频数据:', videoData);
    console.log('  视频分类数量:', Object.keys(videoData).length);

    elements.videoCategories.innerHTML = '';

    for (const [categoryName, files] of Object.entries(videoData)) {
        totalCount += files.length;
        console.log(`  分类: ${categoryName}, 文件数: ${files.length}`);

        const categoryNode = document.createElement('div');
        categoryNode.className = 'tree-node child-node';
        categoryNode.dataset.type = 'video';
        categoryNode.dataset.category = categoryName;
        categoryNode.innerHTML = `
            <span class="node-icon">📁</span>
            <span class="node-label">${categoryName}</span>
            <span class="node-count">${files.length}</span>
        `;

        categoryNode.addEventListener('click', () => selectCategory('video', categoryName));
        elements.videoCategories.appendChild(categoryNode);
        console.log(`  ✅ 已添加视频分类节点: ${categoryName}`);
    }

    elements.videoCount.textContent = totalCount;
    console.log(`  📊 视频总数: ${totalCount}`);
}

// 选择分类
function selectCategory(type, categoryName) {
    // 清除之前的选中状态
    document.querySelectorAll('.tree-node.active').forEach(node => {
        node.classList.remove('active');
    });

    // 设置当前选中状态
    const selectedNode = document.querySelector(`[data-type="${type}"][data-category="${categoryName}"]`);
    if (selectedNode) {
        selectedNode.classList.add('active');
    }

    // 更新当前分类
    currentCategory = { type, categoryName };
    currentFiles = mediaLibrary[type][categoryName] || [];
    currentPage = 1;

    // 更新界面
    updateBreadcrumb();
    updateFileList();
    updatePagination();

    updateStatus(`已选择 ${categoryName}，共 ${currentFiles.length} 个文件`);
}

// 更新面包屑导航
function updateBreadcrumb() {
    if (currentCategory) {
        let typeText;
        if (currentCategory.type === 'task') {
            typeText = '🎯 任务播放列表';
        } else {
            typeText = currentCategory.type === 'audio' ? '音频' : '视频';
        }

        if (currentCategory.type === 'task') {
            elements.currentPath.textContent = typeText;
        } else {
            elements.currentPath.textContent = `${typeText} > ${currentCategory.categoryName}`;
        }
    } else {
        elements.currentPath.textContent = '请选择分类';
    }
}

// 更新文件列表
function updateFileList() {
    if (!currentFiles.length) {
        elements.fileList.innerHTML = `
            <div class="no-selection">
                <div class="no-selection-icon">📂</div>
                <p>该分类下暂无文件</p>
            </div>
        `;
        elements.fileCount.textContent = '0 个文件';
        elements.playAllBtn.style.display = 'none';
        return;
    }

    // 计算分页
    totalPages = Math.ceil(currentFiles.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, currentFiles.length);
    const pageFiles = currentFiles.slice(startIndex, endIndex);

    // 生成文件列表HTML
    elements.fileList.innerHTML = pageFiles.map((file, index) => {
        const globalIndex = startIndex + index;
        const isCurrentFile = currentPlayingFile && currentPlayingFile.path === file.path;
        const isActuallyPlaying = isCurrentFile && currentPlayer && isPlaying;
        const fileIcon = getFileIcon(file.extension);
        const fileSize = formatFileSize(file.size);

        // 如果是任务播放列表，显示文件类型和分类信息
        let categoryInfo = '';
        if (currentCategory && currentCategory.type === 'task') {
            const mediaType = file.mediaType || file.type;
            const typeIcon = mediaType === 'video' ? '🎬' : '🎵';
            const typeName = mediaType === 'video' ? '视频' : '音频';
            categoryInfo = `<span class="file-category">${typeIcon} ${typeName} - ${file.categoryName}</span>`;
        }

        return `
            <div class="file-item ${isActuallyPlaying ? 'playing' : ''}" data-index="${globalIndex}">
                <div class="file-icon">${fileIcon}</div>
                <div class="file-info">
                    <div class="file-name" title="${file.name}">${file.name}</div>
                    <div class="file-details">
                        <span class="file-size">${fileSize}</span>
                        <span class="file-type">${file.extension.toUpperCase()}</span>
                        ${categoryInfo}
                    </div>
                </div>
                <div class="file-actions">
                    <button class="play-btn ${isActuallyPlaying ? 'playing' : ''}"
                            onclick="togglePlayFile(${globalIndex})"
                            title="${isActuallyPlaying ? '暂停' : '播放'}">
                        ${isActuallyPlaying ? '⏸️' : '▶️'}
                    </button>
                </div>
            </div>
        `;
    }).join('');

    // 更新文件计数
    elements.fileCount.textContent = `${currentFiles.length} 个文件`;
    elements.playAllBtn.style.display = currentFiles.length > 0 ? 'block' : 'none';
}

// 初始化事件监听器
function initializeEventListeners() {
    // 工具栏按钮
    elements.refreshBtn.addEventListener('click', loadMediaLibrary);
    elements.audioTabBtn.addEventListener('click', () => switchToMediaType('audio'));
    elements.videoTabBtn.addEventListener('click', () => switchToMediaType('video'));
    elements.openMediaFolderBtn.addEventListener('click', openMediaFolder);
    elements.settingsBtn.addEventListener('click', createMediaFolders);

    // 窗口控制按钮
    elements.minimizeBtn.addEventListener('click', minimizeWindow);
    elements.closeBtn.addEventListener('click', closeWindow);

    // 分页按钮
    elements.prevPageBtn.addEventListener('click', () => changePage(-1));
    elements.nextPageBtn.addEventListener('click', () => changePage(1));
    elements.playAllBtn.addEventListener('click', playAllFiles);

    // 播放控制按钮
    elements.playPauseBtn.addEventListener('click', togglePlayPause);
    elements.prevBtn.addEventListener('click', playPrevious);
    elements.nextBtn.addEventListener('click', playNext);
    elements.stopBtn.addEventListener('click', stopPlayback);

    // 进度条和音量控制
    elements.progressSlider.addEventListener('input', seekTo);
    elements.volumeSlider.addEventListener('input', setVolume);
    elements.muteBtn.addEventListener('click', toggleMute);
    elements.fullscreenBtn.addEventListener('click', toggleFullscreen);
    elements.videoCloseBtn.addEventListener('click', closeVideo);

    // 视频窗口拖动功能
    initVideoWindowDrag();

    // 播放模式
    elements.playModeBtn.addEventListener('click', togglePlayMode);

    // 任务完成弹窗事件
    if (elements.taskModalCloseBtn) {
        elements.taskModalCloseBtn.addEventListener('click', closeTaskCompleteModal);
    }
    if (elements.taskCompleteOkBtn) {
        elements.taskCompleteOkBtn.addEventListener('click', closeTaskCompleteModal);
    }
    if (elements.taskCompleteModal) {
        elements.taskCompleteModal.addEventListener('click', (e) => {
            if (e.target === elements.taskCompleteModal) {
                closeTaskCompleteModal();
            }
        });
    }

    // 右键菜单
    if (elements.playlist) {
        elements.playlist.addEventListener('contextmenu', showContextMenu);
    }
    document.addEventListener('click', hideContextMenu);

    // 键盘快捷键
    document.addEventListener('keydown', handleKeyboard);

    // IPC 事件监听
    ipcRenderer.on('refresh-media-library', loadMediaLibrary);
    ipcRenderer.on('toggle-play', toggleCurrentFile);
    ipcRenderer.on('stop-play', stopPlayback);
    ipcRenderer.on('previous-track', playPrevious);
    ipcRenderer.on('next-track', playNext);
}

// 获取文件图标
function getFileIcon(extension) {
    const iconMap = {
        '.mp3': '🎵', '.wav': '🎵', '.flac': '🎵', '.aac': '🎵', '.ogg': '🎵', '.m4a': '🎵',
        '.mp4': '🎬', '.avi': '🎬', '.mkv': '🎬', '.webm': '🎬', '.mov': '🎬', '.wmv': '🎬', '.flv': '🎬'
    };
    return iconMap[extension.toLowerCase()] || '📄';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// 更新分页
function updatePagination() {
    if (totalPages <= 1) {
        elements.pagination.style.display = 'none';
        return;
    }

    elements.pagination.style.display = 'flex';
    elements.pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
    elements.prevPageBtn.disabled = currentPage <= 1;
    elements.nextPageBtn.disabled = currentPage >= totalPages;
}

// 切换页面
function changePage(direction) {
    const newPage = currentPage + direction;
    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        updateFileList();
        updatePagination();
    }
}

// 切换到指定媒体类型
function switchToMediaType(type) {
    // 更新标签页状态
    elements.audioTabBtn.classList.toggle('active', type === 'audio');
    elements.videoTabBtn.classList.toggle('active', type === 'video');

    // 清除当前选择
    currentCategory = null;
    currentFiles = [];
    currentPage = 1;

    // 清除选中状态
    document.querySelectorAll('.tree-node.active').forEach(node => {
        node.classList.remove('active');
    });

    // 更新界面
    updateBreadcrumb();
    elements.fileList.innerHTML = `
        <div class="no-selection">
            <div class="no-selection-icon">📂</div>
            <p>请从左侧选择${type === 'audio' ? '音频' : '视频'}分类查看文件</p>
        </div>
    `;
    elements.fileCount.textContent = '0 个文件';
    elements.playAllBtn.style.display = 'none';
    elements.pagination.style.display = 'none';
}

// 播放/暂停指定文件
function togglePlayFile(fileIndex) {
    const file = currentFiles[fileIndex];
    if (!file) return;

    // 如果是当前播放的文件，则暂停/继续
    if (currentPlayingFile && currentPlayingFile.path === file.path && currentPlayer) {
        toggleCurrentFile();
        return;
    }

    // 播放新文件（包括重新播放被关闭的视频）
    playFile(file, fileIndex);
}

// 播放文件
function playFile(file, fileIndex) {
    // 停止当前播放
    if (currentPlayer) {
        currentPlayer.pause();
        currentPlayer.currentTime = 0;
    }

    currentPlayingFile = file;

    // 根据文件类型选择播放器
    if (file.type === 'video' || file.extension.toLowerCase().includes('mp4') ||
        file.extension.toLowerCase().includes('avi') || file.extension.toLowerCase().includes('mkv')) {
        elements.videoContainer.style.display = 'block';
        elements.audioContainer.style.display = 'none';
        elements.fullscreenBtn.style.display = 'block';
        currentPlayer = elements.videoPlayer;

        // 视频播放时自动全屏
        setTimeout(() => {
            if (currentPlayer && !currentPlayer.paused) {
                enterVideoFullscreen();
            }
        }, 500); // 延迟500ms确保视频加载
    } else {
        elements.videoContainer.style.display = 'none';
        elements.audioContainer.style.display = 'flex';
        elements.fullscreenBtn.style.display = 'none';
        currentPlayer = elements.audioPlayer;
    }

    // 设置媒体源
    currentPlayer.src = `file://${file.path}`;

    // 更新界面
    elements.trackTitle.textContent = file.name;
    elements.trackArtist.textContent = currentCategory ? currentCategory.categoryName : '未知分类';

    // 设置播放器事件
    setupPlayerEvents();

    // 开始播放
    currentPlayer.play().then(() => {
        isPlaying = true;
        updatePlayButton();
        updateFileList(); // 刷新列表显示播放状态
        updateStatus(`正在播放: ${file.name}`);

        // 启动播放时长统计
        startPlayTimeTracker();
    }).catch(error => {
        console.error('播放失败:', error);
        updateStatus(`播放失败: ${file.name}`);
    });
}

// 切换当前文件播放状态
function toggleCurrentFile() {
    if (!currentPlayer || !currentPlayingFile) return;

    if (isPlaying) {
        currentPlayer.pause();
        isPlaying = false;
    } else {
        currentPlayer.play();
        isPlaying = true;
    }

    updatePlayButton();
    updateFileList();
}

// 播放全部文件
function playAllFiles() {
    if (currentFiles.length === 0) return;
    playFile(currentFiles[0], 0);
}

// 创建媒体文件夹
async function createMediaFolders() {
    try {
        const result = await ipcRenderer.invoke('create-media-directories');
        if (result.success) {
            updateStatus('媒体文件夹创建成功');
            await loadMediaLibrary();
        } else {
            updateStatus('创建媒体文件夹失败: ' + result.error);
        }
    } catch (error) {
        console.error('创建媒体文件夹失败:', error);
        updateStatus('创建媒体文件夹失败');
    }
}

// 打开媒体文件夹
async function openMediaFolder() {
    try {
        await ipcRenderer.invoke('open-media-folder');
        updateStatus('已打开媒体文件夹');
    } catch (error) {
        console.error('打开媒体文件夹失败:', error);
        updateStatus('打开媒体文件夹失败');
    }
}

// 添加文件到播放列表
async function addFilesToPlaylist(filePaths) {
    for (const filePath of filePaths) {
        try {
            const fileInfo = await ipcRenderer.invoke('get-file-info', filePath);
            if (fileInfo) {
                playlist.push({
                    path: filePath,
                    name: fileInfo.name,
                    size: fileInfo.size,
                    extension: fileInfo.extension,
                    type: getMediaType(fileInfo.extension)
                });
            }
        } catch (error) {
            console.error('添加文件失败:', error);
        }
    }
    
    updatePlaylistDisplay();
    updateStatus(`已添加 ${filePaths.length} 个文件到播放列表`);
}

// 获取媒体类型
function getMediaType(extension) {
    const audioExtensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg'];
    const videoExtensions = ['.mp4', '.avi', '.mkv', '.webm', '.mov', '.wmv'];
    
    if (audioExtensions.includes(extension.toLowerCase())) {
        return 'audio';
    } else if (videoExtensions.includes(extension.toLowerCase())) {
        return 'video';
    }
    return 'unknown';
}

// 更新播放列表显示
function updatePlaylistDisplay() {
    // 播放列表现在使用文件列表区域显示，不需要单独的播放列表元素
    console.log('播放列表更新 - 当前播放列表长度:', playlist.length);

    // 如果有播放列表元素，更新计数
    if (elements.playlistCount) {
        elements.playlistCount.textContent = `${playlist.length} 首`;
    }

    // 播放列表内容通过文件列表区域显示，这里不需要额外处理
}

// 播放指定曲目
function playTrack(index) {
    if (index < 0 || index >= playlist.length) return;

    currentTrackIndex = index;
    const track = playlist[index];

    // 停止当前播放
    if (currentPlayer) {
        currentPlayer.pause();
        currentPlayer.currentTime = 0;
    }

    // 更新当前播放文件信息
    currentPlayingFile = {
        name: track.name,
        path: track.path,
        size: track.size,
        extension: track.extension,
        type: track.type
    };

    // 根据媒体类型选择播放器
    if (track.type === 'video') {
        elements.videoContainer.style.display = 'block';
        elements.audioContainer.style.display = 'none';
        elements.fullscreenBtn.style.display = 'block';
        currentPlayer = elements.videoPlayer;
    } else {
        elements.videoContainer.style.display = 'none';
        elements.audioContainer.style.display = 'flex';
        elements.fullscreenBtn.style.display = 'none';
        currentPlayer = elements.audioPlayer;
    }

    // 设置媒体源
    currentPlayer.src = `file://${track.path}`;

    // 更新界面
    elements.trackTitle.textContent = track.name;
    elements.trackArtist.textContent = path.dirname(track.path);

    // 设置播放器事件
    setupPlayerEvents();

    // 开始播放
    currentPlayer.play().then(() => {
        isPlaying = true;
        updatePlayButton();
        updatePlaylistDisplay();
        updateFileList(); // 更新文件列表以同步播放状态
        updateStatus(`正在播放: ${track.name}`);
    }).catch(error => {
        console.error('播放失败:', error);
        updateStatus(`播放失败: ${track.name}`);
    });
}

// 设置播放器事件
function setupPlayerEvents() {
    if (!currentPlayer) return;

    // 移除旧的事件监听器
    currentPlayer.removeEventListener('timeupdate', updateProgress);
    currentPlayer.removeEventListener('ended', onTrackEnded);
    currentPlayer.removeEventListener('loadedmetadata', updateDuration);

    // 添加新的事件监听器
    currentPlayer.addEventListener('timeupdate', updateProgress);
    currentPlayer.addEventListener('ended', onTrackEnded);
    currentPlayer.addEventListener('loadedmetadata', updateDuration);
    
    // 设置音量
    currentPlayer.volume = elements.volumeSlider.value / 100;
}

// 播放/暂停切换
function togglePlayPause() {
    if (!currentPlayer) {
        if (playlist.length > 0) {
            playTrack(0);
        }
        return;
    }

    if (isPlaying) {
        currentPlayer.pause();
        isPlaying = false;
    } else {
        currentPlayer.play();
        isPlaying = true;
    }
    
    updatePlayButton();
}

// 更新播放按钮
function updatePlayButton() {
    const icon = elements.playPauseBtn.querySelector('.icon');
    icon.textContent = isPlaying ? '⏸️' : '▶️';
}

// 播放上一首
function playPrevious() {
    if (playlist.length === 0) return;
    
    let nextIndex;
    if (playMode === 'random') {
        nextIndex = Math.floor(Math.random() * playlist.length);
    } else {
        nextIndex = currentTrackIndex - 1;
        if (nextIndex < 0) {
            nextIndex = playlist.length - 1;
        }
    }
    
    playTrack(nextIndex);
}

// 播放下一首
function playNext() {
    if (playlist.length === 0) return;
    
    let nextIndex;
    if (playMode === 'random') {
        nextIndex = Math.floor(Math.random() * playlist.length);
    } else {
        nextIndex = currentTrackIndex + 1;
        if (nextIndex >= playlist.length) {
            nextIndex = 0;
        }
    }
    
    playTrack(nextIndex);
}

// 停止播放
function stopPlayback() {
    if (currentPlayer) {
        currentPlayer.pause();
        currentPlayer.currentTime = 0;
        isPlaying = false;
        updatePlayButton();
        updateProgress();
        updateStatus('已停止');

        // 上报最终播放统计
        if (totalPlayTime > 0) {
            reportPlayStatistics();
        }
    }
}

// 跳转到指定位置
function seekTo() {
    if (currentPlayer && currentPlayer.duration) {
        const seekTime = (elements.progressSlider.value / 100) * currentPlayer.duration;
        currentPlayer.currentTime = seekTime;
    }
}

// 设置音量
function setVolume() {
    if (currentPlayer) {
        currentPlayer.volume = elements.volumeSlider.value / 100;
    }
}

// 静音切换
function toggleMute() {
    if (currentPlayer) {
        currentPlayer.muted = !currentPlayer.muted;
        const icon = elements.muteBtn.querySelector('.icon');
        icon.textContent = currentPlayer.muted ? '🔇' : '🔊';
    }
}

// 全屏切换
function toggleFullscreen() {
    if (elements.videoPlayer.requestFullscreen) {
        elements.videoPlayer.requestFullscreen();
    }
}

// 切换播放模式
function togglePlayMode() {
    const modes = ['sequence', 'repeat', 'random'];
    const currentIndex = modes.indexOf(playMode);
    playMode = modes[(currentIndex + 1) % modes.length];
    
    const modeNames = {
        'sequence': '顺序播放',
        'repeat': '循环播放',
        'random': '随机播放'
    };
    
    elements.playMode.textContent = modeNames[playMode];
    
    const icons = {
        'sequence': '🔁',
        'repeat': '🔂',
        'random': '🔀'
    };
    
    elements.playModeBtn.querySelector('.icon').textContent = icons[playMode];
}

// 更新进度
function updateProgress() {
    if (currentPlayer && currentPlayer.duration) {
        const progress = (currentPlayer.currentTime / currentPlayer.duration) * 100;
        elements.progressSlider.value = progress;
        
        elements.currentTime.textContent = formatTime(currentPlayer.currentTime);
    }
}

// 更新总时长
function updateDuration() {
    if (currentPlayer && currentPlayer.duration) {
        elements.totalTime.textContent = formatTime(currentPlayer.duration);
    }
}

// 格式化时间
function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// 曲目播放结束
function onTrackEnded() {
    if (playMode === 'repeat') {
        playTrack(currentTrackIndex);
    } else {
        playNext();
    }
}

// 更新状态
function updateStatus(message) {
    elements.statusText.textContent = message;
}

// 显示右键菜单
function showContextMenu(event) {
    event.preventDefault();
    const target = event.target.closest('.playlist-item');
    if (!target) return;
    
    const rect = target.getBoundingClientRect();
    elements.contextMenu.style.display = 'block';
    elements.contextMenu.style.left = event.clientX + 'px';
    elements.contextMenu.style.top = event.clientY + 'px';
    
    // 设置当前选中的项目
    elements.contextMenu.dataset.targetIndex = target.dataset.index;
}

// 隐藏右键菜单
function hideContextMenu() {
    elements.contextMenu.style.display = 'none';
}

// 键盘快捷键处理
function handleKeyboard(event) {
    switch (event.code) {
        case 'Space':
            event.preventDefault();
            togglePlayPause();
            break;
        case 'ArrowLeft':
            if (event.ctrlKey) {
                event.preventDefault();
                playPrevious();
            }
            break;
        case 'ArrowRight':
            if (event.ctrlKey) {
                event.preventDefault();
                playNext();
            }
            break;
        case 'F11':
            event.preventDefault();
            toggleFullscreen();
            break;
        case 'Escape':
            event.preventDefault();
            if (document.fullscreenElement) {
                exitVideoFullscreen();
            }
            break;
    }
}

// 关闭视频
function closeVideo() {
    if (currentPlayer && currentPlayer === elements.videoPlayer) {
        stopPlayback();
        elements.videoContainer.style.display = 'none';
        elements.audioContainer.style.display = 'flex';
        elements.fullscreenBtn.style.display = 'none';

        // 退出全屏
        if (document.fullscreenElement) {
            exitVideoFullscreen();
        }

        // 清除当前播放状态，但保留文件信息以便重新播放
        currentPlayer = null;
        isPlaying = false;
        updatePlayButton();
        updateFileList();

        updateStatus('视频已关闭');
    }
}

// 进入视频全屏
function enterVideoFullscreen() {
    if (elements.videoPlayer && elements.videoContainer.style.display === 'block') {
        if (elements.videoPlayer.requestFullscreen) {
            elements.videoPlayer.requestFullscreen();
        } else if (elements.videoPlayer.webkitRequestFullscreen) {
            elements.videoPlayer.webkitRequestFullscreen();
        } else if (elements.videoPlayer.msRequestFullscreen) {
            elements.videoPlayer.msRequestFullscreen();
        }
    }
}

// 退出视频全屏
function exitVideoFullscreen() {
    if (document.exitFullscreen) {
        document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
    }
}

// 窗口控制函数
async function minimizeWindow() {
    try {
        await ipcRenderer.invoke('minimize-window');
    } catch (error) {
        console.error('最小化窗口失败:', error);
    }
}

async function closeWindow() {
    try {
        await ipcRenderer.invoke('close-window');
    } catch (error) {
        console.error('关闭窗口失败:', error);
    }
}

// 添加视频点击事件监听器
document.addEventListener('DOMContentLoaded', () => {
    // 视频点击自动全屏
    elements.videoPlayer.addEventListener('click', () => {
        if (!document.fullscreenElement) {
            enterVideoFullscreen();
        }
    });

    // 全屏状态变化监听
    document.addEventListener('fullscreenchange', () => {
        if (!document.fullscreenElement) {
            // 退出全屏时的处理
            console.log('已退出全屏模式');
        }
    });

    // 兼容性事件监听
    document.addEventListener('webkitfullscreenchange', () => {
        if (!document.webkitFullscreenElement) {
            console.log('已退出全屏模式 (webkit)');
        }
    });
});

// 视频窗口拖动功能
function initVideoWindowDrag() {
    const videoContainer = elements.videoContainer;
    const titlebar = document.getElementById('videoTitlebar');
    let isDragging = false;
    let currentX;
    let currentY;
    let initialX;
    let initialY;
    let xOffset = 0;
    let yOffset = 0;

    function dragStart(e) {
        if (e.target === titlebar || titlebar.contains(e.target)) {
            // 排除关闭按钮
            if (e.target.closest('.video-close-btn')) {
                return;
            }

            if (e.type === "touchstart") {
                initialX = e.touches[0].clientX - xOffset;
                initialY = e.touches[0].clientY - yOffset;
            } else {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
            }

            if (e.target === titlebar || titlebar.contains(e.target)) {
                isDragging = true;
                titlebar.style.cursor = 'grabbing';
            }
        }
    }

    function dragEnd(e) {
        initialX = currentX;
        initialY = currentY;
        isDragging = false;
        titlebar.style.cursor = 'move';
    }

    function drag(e) {
        if (isDragging) {
            e.preventDefault();

            if (e.type === "touchmove") {
                currentX = e.touches[0].clientX - initialX;
                currentY = e.touches[0].clientY - initialY;
            } else {
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
            }

            xOffset = currentX;
            yOffset = currentY;

            // 限制拖动范围，确保窗口不会完全移出屏幕
            const rect = videoContainer.getBoundingClientRect();
            const maxX = window.innerWidth - 100; // 至少保留100px可见
            const maxY = window.innerHeight - 100;
            const minX = -(rect.width - 100);
            const minY = 0;

            xOffset = Math.max(minX, Math.min(maxX, xOffset));
            yOffset = Math.max(minY, Math.min(maxY, yOffset));

            setTranslate(xOffset, yOffset, videoContainer);
        }
    }

    function setTranslate(xPos, yPos, el) {
        el.style.transform = `translate(calc(-50% + ${xPos}px), calc(-50% + ${yPos}px))`;
    }

    // 鼠标事件
    titlebar.addEventListener("mousedown", dragStart, false);
    document.addEventListener("mouseup", dragEnd, false);
    document.addEventListener("mousemove", drag, false);

    // 触摸事件（移动设备支持）
    titlebar.addEventListener("touchstart", dragStart, false);
    document.addEventListener("touchend", dragEnd, false);
    document.addEventListener("touchmove", drag, false);

    // 重置窗口位置
    function resetVideoWindowPosition() {
        xOffset = 0;
        yOffset = 0;
        setTranslate(0, 0, videoContainer);
    }

    // 当视频窗口显示时重置位置
    const originalShowVideo = () => {
        if (videoContainer.style.display === 'block') {
            resetVideoWindowPosition();
        }
    };

    // 监听视频窗口显示
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                if (videoContainer.style.display === 'block') {
                    resetVideoWindowPosition();
                    // 更新视频标题
                    updateVideoTitle();
                }
            }
        });
    });

    observer.observe(videoContainer, { attributes: true });
}

// 更新视频标题
function updateVideoTitle() {
    const videoTitle = document.getElementById('videoTitle');
    if (currentPlayingFile && videoTitle) {
        videoTitle.textContent = currentPlayingFile.name || '视频播放器';
    }
}

// 启动媒体库自动刷新定时器
function startMediaRefreshTimer() {
    // 清除现有定时器（如果存在）
    if (mediaRefreshInterval) {
        clearInterval(mediaRefreshInterval);
    }

    // 设置5秒刷新一次
    mediaRefreshInterval = setInterval(async () => {
        try {
            console.log('自动刷新媒体库...');
            await loadMediaLibraryQuietly();
        } catch (error) {
            console.error('自动刷新媒体库失败:', error);
        }
    }, 5000);

    console.log('媒体库自动刷新定时器已启动 (每5秒)');
}

// 停止媒体库自动刷新定时器
function stopMediaRefreshTimer() {
    if (mediaRefreshInterval) {
        clearInterval(mediaRefreshInterval);
        mediaRefreshInterval = null;
        console.log('媒体库自动刷新定时器已停止');
    }
}

// 静默加载媒体库（不显示状态信息）
async function loadMediaLibraryQuietly() {
    try {
        const mediaData = await ipcRenderer.invoke('get-builtin-media');

        // 检查是否有变化
        const hasChanges = JSON.stringify(mediaLibrary) !== JSON.stringify(mediaData);

        if (hasChanges) {
            console.log('检测到媒体库变化，更新界面...');
            mediaLibrary = mediaData;

            // 保存当前选择状态
            const currentSelection = currentCategory;

            updateDirectoryTree();

            // 如果有当前选择的分类，重新选择以更新文件列表
            if (currentSelection && mediaLibrary[currentSelection.type] &&
                mediaLibrary[currentSelection.type][currentSelection.categoryName]) {
                selectCategory(currentSelection.type, currentSelection.categoryName);
            }

            updateStatus('媒体库已自动更新');
        }
    } catch (error) {
        console.error('静默加载媒体库失败:', error);
    }
}

// 应用关闭时清理定时器
window.addEventListener('beforeunload', () => {
    stopMediaRefreshTimer();
    stopPatientInfoChecker();
    stopPlayTimeTracker();
    saveTaskList(); // 保存任务列表
});

// ==================== 任务管理系统 ====================

// 加载任务列表
async function loadTaskList() {
    try {
        const data = await window.electronAPI.readFile(taskFilePath);
        if (data) {
            taskList = JSON.parse(data);
            console.log('任务列表加载成功:', taskList.length, '个任务');
        } else {
            taskList = [];
            console.log('任务文件不存在，初始化空任务列表');
        }
    } catch (error) {
        console.warn('加载任务列表失败:', error);
        taskList = [];
    }
}

// 保存任务列表
async function saveTaskList() {
    try {
        await window.electronAPI.writeFile(taskFilePath, JSON.stringify(taskList, null, 2));
        console.log('任务列表保存成功');
    } catch (error) {
        console.error('保存任务列表失败:', error);
    }
}

// 添加新任务
function addTask(patientData) {
    console.log('添加任务，接收到的数据:', patientData);

    // 从结构化数据中提取信息
    const basicInfo = patientData.basic_info || {};
    const trainingTask = patientData.training_task || {};

    const task = {
        id: Date.now().toString(),
        patientName: basicInfo.name || patientData.patient_name || '',
        visitNumber: basicInfo.id || patientData.visit_number || '',
        taskName: trainingTask.training_project || patientData.rehabilitation_content?.[0] || '康复训练',
        duration: trainingTask.training_duration || patientData.training_duration || 0, // 分钟
        mediaNames: patientData.rehabilitation_content || [trainingTask.training_project] || [],
        createdAt: new Date().toISOString(),
        status: 'pending', // pending, active, completed
        progress: 0, // 完成进度 0-100
        playedDuration: 0 // 已播放时长（秒）
    };

    console.log('创建的任务对象:', task);

    // 检查是否已存在相同的任务
    const existingTask = taskList.find(t =>
        t.patientName === task.patientName &&
        t.visitNumber === task.visitNumber &&
        t.taskName === task.taskName
    );

    if (existingTask) {
        console.log('任务已存在，更新任务信息');
        Object.assign(existingTask, task);
    } else {
        taskList.push(task);
        console.log('新任务已添加:', task);
    }

    saveTaskList();
    updateTaskDisplay();
    updateStatus(`新任务: ${task.taskName} (${task.patientName})`);
}

// 更新任务显示
function updateTaskDisplay() {
    if (!elements.taskList || !elements.taskCount) return;

    // 更新任务计数
    const pendingTasks = taskList.filter(task => task.status !== 'completed');
    elements.taskCount.textContent = pendingTasks.length;

    // 清空任务列表
    elements.taskList.innerHTML = '';

    if (pendingTasks.length === 0) {
        // 显示无任务状态
        elements.taskList.innerHTML = `
            <div class="no-tasks">
                <div class="no-tasks-icon">📋</div>
                <p>暂无康复任务</p>
            </div>
        `;

        // 清空顶部信息
        if (!currentTask) {
            patientInfo.name = '';
            patientInfo.visitNumber = '';
            patientInfo.mediaNames = [];
            patientInfo.sessionDuration = 0;
            updatePatientInfoDisplay();
        }
        return;
    }

    // 显示任务列表
    pendingTasks.forEach(task => {
        const taskElement = createTaskElement(task);
        elements.taskList.appendChild(taskElement);
    });

    // 如果没有当前任务，默认选择第一条任务
    if (!currentTask && pendingTasks.length > 0) {
        const firstTask = pendingTasks[0];
        firstTask.status = 'active';
        currentTask = firstTask;

        // 更新顶部信息
        patientInfo.name = firstTask.patientName;
        patientInfo.visitNumber = firstTask.visitNumber;
        patientInfo.mediaNames = [firstTask.taskName];
        patientInfo.sessionDuration = firstTask.playedDuration;
        updatePatientInfoDisplay();

        // 更新播放器信息
        updatePlayerInfo(firstTask);

        console.log('默认选择第一条任务:', firstTask);

        // 重新更新显示以反映选中状态
        updateTaskDisplay();
        return;
    }
}

// 创建任务元素
function createTaskElement(task) {
    const taskDiv = document.createElement('div');
    taskDiv.className = `task-item ${task.status === 'active' ? 'active' : ''}`;
    taskDiv.dataset.taskId = task.id;

    const progressPercent = Math.min(100, (task.playedDuration / (task.duration * 60)) * 100);

    taskDiv.innerHTML = `
        <div class="task-info">
            <h4 class="task-name">${task.taskName}</h4>
            <div class="task-details">
                <span class="task-patient">${task.patientName}</span>
                <span class="task-duration">${task.duration}分钟</span>
            </div>
            <div class="task-progress">
                <div class="task-progress-bar" style="width: ${progressPercent}%"></div>
            </div>
        </div>
    `;

    // 所有任务项都可以点击，在selectTask中处理播放状态检查
    taskDiv.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        selectTask(task);
    });

    return taskDiv;
}

// 选择任务（单选模式）
function selectTask(task) {
    console.log('选择任务:', task);

    // 检查是否正在播放其他任务
    if (isPlaying && currentTask && currentTask.id !== task.id) {
        console.log('正在播放其他任务，显示警告');
        // 弹窗提醒用户等待当前任务完成
        showTaskSwitchWarning();
        return;
    }

    // 如果选择的是当前任务，允许重新选择（可能需要重新加载）
    if (currentTask && currentTask.id === task.id) {
        console.log('重新选择当前任务');
        // 可以重新加载当前任务，不返回
    }

    // 清除所有任务的active状态
    taskList.forEach(t => {
        if (t.status === 'active') {
            t.status = 'pending';
        }
    });

    // 设置当前任务为active
    task.status = 'active';
    currentTask = task;

    // 更新任务显示（刷新选中状态）
    updateTaskDisplay();

    // 加载任务到播放列表
    loadTaskToPlaylist(task);
}

// 显示任务切换警告弹窗
function showTaskSwitchWarning() {
    // 创建弹窗元素
    const modal = document.createElement('div');
    modal.className = 'task-switch-modal';
    modal.innerHTML = `
        <div class="task-switch-modal-content">
            <div class="task-switch-modal-header">
                <h3>任务进行中</h3>
            </div>
            <div class="task-switch-modal-body">
                <div class="warning-icon">⚠️</div>
                <p>请等待上一位完成任务后方可开始</p>
                <p class="current-task-info">当前任务：${currentTask ? currentTask.taskName : '未知任务'}</p>
            </div>
            <div class="task-switch-modal-footer">
                <button class="modal-btn modal-btn-primary" onclick="closeTaskSwitchModal()">我知道了</button>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(modal);

    // 添加点击背景关闭功能
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeTaskSwitchModal();
        }
    });

    console.log('显示任务切换警告弹窗');
}

// 关闭任务切换警告弹窗
window.closeTaskSwitchModal = function() {
    const modal = document.querySelector('.task-switch-modal');
    if (modal) {
        modal.remove();
    }
}

// 加载任务到播放列表
function loadTaskToPlaylist(task) {
    console.log('🎯 加载任务到播放列表:', task);

    // 重置播放时长统计
    totalPlayTime = task.playedDuration || 0;

    // 更新就诊信息 - 显示当前任务信息
    patientInfo.name = task.patientName;
    patientInfo.visitNumber = task.visitNumber;
    patientInfo.mediaNames = [task.taskName]; // 显示任务名称而不是媒体文件名
    patientInfo.sessionDuration = totalPlayTime;
    updatePatientInfoDisplay();

    // 在媒体库中搜索匹配的文件并加载到混合播放列表
    loadTaskMediaToMixedPlaylist(task.mediaNames);

    // 更新播放器信息显示
    updatePlayerInfo(task);

    // 保存任务列表
    saveTaskList();

    updateStatus(`已加载任务: ${task.taskName}，请手动开始播放`);
}

// 更新播放器信息显示
function updatePlayerInfo(task) {
    // 更新音频播放器的显示信息
    if (elements.trackTitle) {
        elements.trackTitle.textContent = task.taskName;
    }
    if (elements.trackArtist) {
        elements.trackArtist.textContent = `${task.patientName} - ${task.duration}分钟康复训练`;
    }

    console.log('播放器信息已更新:', task.taskName);
}

// 更新任务进度
function updateTaskProgress() {
    if (!currentTask) return;

    // 更新任务的播放时长
    currentTask.playedDuration = totalPlayTime;

    // 计算进度百分比
    const targetDuration = currentTask.duration * 60; // 转换为秒
    const progress = Math.min(100, (totalPlayTime / targetDuration) * 100);
    currentTask.progress = progress;

    // 检查任务是否完成
    if (totalPlayTime >= targetDuration && currentTask.status === 'active') {
        completeTask(currentTask);
    }

    // 更新显示
    updateTaskDisplay();
    saveTaskList();
}

// 完成任务
async function completeTask(task) {
    console.log('任务完成:', task);

    task.status = 'completed';
    task.progress = 100;
    task.completedAt = new Date().toISOString();

    // 停止播放
    if (isPlaying) {
        pauseMedia();
    }

    // 显示完成弹窗
    showTaskCompleteModal(task);

    // 上报任务完成统计
    await reportTaskCompletion(task);

    // 从任务列表中移除
    const taskIndex = taskList.findIndex(t => t.id === task.id);
    if (taskIndex >= 0) {
        taskList.splice(taskIndex, 1);
        console.log('任务已从列表中移除');
    }

    // 清除当前任务
    currentTask = null;

    // 重置播放状态
    isPlaying = false;
    totalPlayTime = 0;

    // 停止播放时长统计
    if (playTimeTracker) {
        clearInterval(playTimeTracker);
        playTimeTracker = null;
    }

    // 更新显示
    updateTaskDisplay();
    saveTaskList();

    updateStatus(`任务完成: ${task.taskName}，可以开始下一个任务`);
}

// 显示任务完成弹窗
function showTaskCompleteModal(task) {
    if (!elements.taskCompleteModal) return;

    // 更新弹窗内容
    if (elements.completeTaskName) {
        elements.completeTaskName.textContent = task.taskName;
    }
    if (elements.completeTaskDuration) {
        elements.completeTaskDuration.textContent = `时长 ${task.duration} 分钟`;
    }

    // 显示弹窗
    elements.taskCompleteModal.style.display = 'flex';

    // 播放完成音效（可选）
    playCompletionSound();
}

// 关闭任务完成弹窗
function closeTaskCompleteModal() {
    if (elements.taskCompleteModal) {
        elements.taskCompleteModal.style.display = 'none';
    }
}

// 播放完成音效
function playCompletionSound() {
    try {
        // 创建简单的完成音效
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
        oscillator.frequency.setValueAtTime(1200, audioContext.currentTime + 0.2);

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
        console.warn('播放完成音效失败:', error);
    }
}

// 上报任务完成统计
async function reportTaskCompletion(task) {
    try {
        const data = {
            patient_name: task.patientName,
            visit_number: task.visitNumber,
            play_duration: task.playedDuration,
            media_names: task.mediaNames,
            current_media: task.taskName,
            task_duration: task.duration * 60, // 转换为秒
            completion_rate: task.progress,
            timestamp: new Date().toISOString()
        };

        const response = await fetch(`${httpServerUrl}/api/report_play_statistics`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            console.log('任务完成统计上报成功:', data);
            return true;
        } else {
            console.warn('任务完成统计上报失败:', response.status);
            return false;
        }
    } catch (error) {
        console.warn('上报任务完成统计失败:', error.message);
        return false;
    }
}

// ==================== 就诊信息管理和httpServer通信 ====================

// 启动就诊信息检查定时器
function startPatientInfoChecker() {
    // 立即检查一次
    checkPatientInfo();

    // 每5秒检查一次
    patientInfoCheckInterval = setInterval(checkPatientInfo, 5000);
    console.log('就诊信息检查定时器已启动');
}

// 停止就诊信息检查定时器
function stopPatientInfoChecker() {
    if (patientInfoCheckInterval) {
        clearInterval(patientInfoCheckInterval);
        patientInfoCheckInterval = null;
        console.log('就诊信息检查定时器已停止');
    }
}

// 检查就诊信息
async function checkPatientInfo() {
    try {
        const response = await fetch(`${httpServerUrl}/api/current_visit`);
        if (response.ok) {
            const data = await response.json();
            updatePatientInfo(data);
        } else {
            console.warn('获取就诊信息失败:', response.status);
        }
    } catch (error) {
        console.warn('连接httpServer失败:', error.message);
    }
}

// 更新就诊信息
function updatePatientInfo(data) {
    let hasChanges = false;

    // 检查基本信息是否有变化
    if (data.patient_name !== patientInfo.name || data.visit_number !== patientInfo.visitNumber) {
        patientInfo.name = data.patient_name || '';
        patientInfo.visitNumber = data.visit_number || '';
        hasChanges = true;
    }

    // 处理康复内容 - 支持编码和文件名
    let newMediaNames = [];

    // 处理原始康复内容
    const rawContent = data.rehabilitation_content || [];

    // 处理编码映射的内容
    const processedContent = processMediaCodes(data);

    // 合并处理结果
    newMediaNames = [...rawContent, ...processedContent];

    // 去重
    newMediaNames = [...new Set(newMediaNames)];

    // 检查康复内容是否有变化
    if (JSON.stringify(newMediaNames) !== JSON.stringify(patientInfo.mediaNames)) {
        patientInfo.mediaNames = newMediaNames;
        hasChanges = true;

        // 添加新任务到任务列表
        if (newMediaNames.length > 0) {
            addTask(data);
        }

        // 注意：这里不自动加载到播放列表，只有点击任务时才加载
        // 康复内容通过任务列表管理，用户手动选择
    }

    if (hasChanges) {
        updatePatientInfoDisplay();
        console.log('就诊信息已更新:', patientInfo);
        console.log('处理后的媒体内容:', newMediaNames);
    }
}

// 处理音视频编码映射
function processMediaCodes(data) {
    const processedNames = [];

    try {
        // 处理训练任务中的编码
        const trainingTask = data.training_task || {};

        // 处理各种编码字段
        const codeFields = ['media_code', 'audio_code', 'video_code', 'content_code', 'item_code'];

        for (const field of codeFields) {
            if (trainingTask[field]) {
                const code = String(trainingTask[field]);
                const mediaName = getMediaNameByCode(code);
                if (mediaName && !mediaName.startsWith('未知编码_')) {
                    processedNames.push(mediaName);
                    console.log(`编码映射: ${field}=${code} -> ${mediaName}`);
                }
            }
        }

        // 处理编码列表
        if (trainingTask.media_codes) {
            let codes = trainingTask.media_codes;
            if (typeof codes === 'string') {
                codes = codes.split(',').map(c => c.trim());
            }
            if (Array.isArray(codes)) {
                for (const code of codes) {
                    const mediaName = getMediaNameByCode(String(code));
                    if (mediaName && !mediaName.startsWith('未知编码_')) {
                        processedNames.push(mediaName);
                        console.log(`编码列表映射: ${code} -> ${mediaName}`);
                    }
                }
            }
        }

        // 处理媒体列表
        if (trainingTask.media_list && Array.isArray(trainingTask.media_list)) {
            for (const mediaItem of trainingTask.media_list) {
                if (mediaItem.code) {
                    const code = String(mediaItem.code);
                    const mediaName = getMediaNameByCode(code);
                    if (mediaName && !mediaName.startsWith('未知编码_')) {
                        processedNames.push(mediaName);
                        console.log(`媒体列表映射: ${code} -> ${mediaName}`);
                    }
                }
            }
        }

        // 处理根级别的编码字段
        const rootFields = ['item_code', 'content_code'];
        for (const field of rootFields) {
            if (data[field]) {
                const code = String(data[field]);
                const mediaName = getMediaNameByCode(code);
                if (mediaName && !mediaName.startsWith('未知编码_')) {
                    processedNames.push(mediaName);
                    console.log(`根级编码映射: ${field}=${code} -> ${mediaName}`);
                }
            }
        }

    } catch (error) {
        console.error('处理音视频编码映射异常:', error);
    }

    return processedNames;
}

// 更新就诊信息显示
function updatePatientInfoDisplay() {
    if (elements.patientName) {
        elements.patientName.textContent = patientInfo.name || '-';
    }

    if (elements.visitNumber) {
        elements.visitNumber.textContent = patientInfo.visitNumber || '-';
    }

    if (elements.mediaNames) {
        const mediaText = patientInfo.mediaNames.length > 0
            ? patientInfo.mediaNames.join(' | ')
            : '-';
        elements.mediaNames.textContent = mediaText;

        // 如果文本过长，启用滚动动画
        const container = elements.mediaNames.parentElement;
        if (container && elements.mediaNames.scrollWidth > container.clientWidth) {
            elements.mediaNames.classList.remove('no-scroll');
        } else {
            elements.mediaNames.classList.add('no-scroll');
        }
    }

    if (elements.sessionDuration) {
        // 如果有当前任务，显示"已播放时长/总时长"，否则只显示已播放时长
        if (currentTask) {
            const playedMinutes = Math.floor(patientInfo.sessionDuration / 60);
            const totalMinutes = currentTask.duration;
            elements.sessionDuration.textContent = `${playedMinutes}/${totalMinutes}分钟`;
        } else {
            const minutes = Math.floor(patientInfo.sessionDuration / 60);
            elements.sessionDuration.textContent = `${minutes}分钟`;
        }
    }
}

// 添加媒体到播放列表（自动播放）
function addMediaToPlaylist(mediaNames) {
    console.log('收到康复内容:', mediaNames);

    // 在媒体库中搜索匹配的文件
    const matchedFiles = [];

    for (const mediaName of mediaNames) {
        // 在音频和视频库中搜索
        for (const [type, categories] of Object.entries(mediaLibrary)) {
            for (const [categoryName, files] of Object.entries(categories)) {
                for (const file of files) {
                    // 模糊匹配文件名
                    if (file.name.toLowerCase().includes(mediaName.toLowerCase()) ||
                        mediaName.toLowerCase().includes(file.name.toLowerCase())) {
                        matchedFiles.push({
                            ...file,
                            type,
                            categoryName,
                            mediaName
                        });
                    }
                }
            }
        }
    }

    if (matchedFiles.length > 0) {
        console.log('找到匹配的媒体文件:', matchedFiles);

        // 自动播放第一个匹配的文件
        const firstFile = matchedFiles[0];
        selectCategory(firstFile.type, firstFile.categoryName);

        // 延迟播放，确保界面更新完成
        setTimeout(() => {
            const fileIndex = currentFiles.findIndex(f => f.path === firstFile.path);
            if (fileIndex >= 0) {
                playFile(firstFile, fileIndex);
                updateStatus(`开始播放康复内容: ${firstFile.name}`);
            }
        }, 500);
    } else {
        console.warn('未找到匹配的媒体文件:', mediaNames);
        updateStatus('未找到指定的康复内容');
    }
}

// 加载媒体到播放列表（不自动播放）
function loadMediaToPlaylist(mediaNames) {
    console.log('加载康复内容到播放列表:', mediaNames);

    // 在媒体库中搜索匹配的文件
    const matchedFiles = [];

    for (const mediaName of mediaNames) {
        // 在音频和视频库中搜索
        for (const [type, categories] of Object.entries(mediaLibrary)) {
            for (const [categoryName, files] of Object.entries(categories)) {
                for (const file of files) {
                    // 模糊匹配文件名
                    if (file.name.toLowerCase().includes(mediaName.toLowerCase()) ||
                        mediaName.toLowerCase().includes(file.name.toLowerCase())) {
                        matchedFiles.push({
                            ...file,
                            type,
                            categoryName,
                            mediaName
                        });
                    }
                }
            }
        }
    }

    if (matchedFiles.length > 0) {
        console.log('找到匹配的媒体文件:', matchedFiles);

        // 只选择分类和显示文件，不自动播放
        const firstFile = matchedFiles[0];
        selectCategory(firstFile.type, firstFile.categoryName);

        updateStatus(`已加载康复内容: ${matchedFiles.length}个文件，请手动开始播放`);
    } else {
        console.warn('未找到匹配的媒体文件:', mediaNames);
        updateStatus('未找到指定的康复内容');
    }
}

// 启动播放时长统计
function startPlayTimeTracker() {
    if (playTimeTracker) return;

    currentSessionStartTime = Date.now();
    playTimeTracker = setInterval(() => {
        if (isPlaying && currentPlayer && !currentPlayer.paused) {
            totalPlayTime += 1; // 每秒增加1秒
            patientInfo.sessionDuration = totalPlayTime;
            updatePatientInfoDisplay();

            // 更新任务进度
            updateTaskProgress();

            // 更新播放器显示的时长信息
            if (currentTask && elements.trackArtist) {
                const playedMinutes = Math.floor(totalPlayTime / 60);
                const totalMinutes = currentTask.duration;
                elements.trackArtist.textContent = `${currentTask.patientName} - ${playedMinutes}/${totalMinutes}分钟康复训练`;
            }

            // 每分钟上报一次播放统计
            if (totalPlayTime % 60 === 0) {
                reportPlayStatistics();
            }
        }
    }, 1000);

    console.log('播放时长统计已启动');
}

// 停止播放时长统计
function stopPlayTimeTracker() {
    if (playTimeTracker) {
        clearInterval(playTimeTracker);
        playTimeTracker = null;
        console.log('播放时长统计已停止');
    }
}

// 上报播放统计
async function reportPlayStatistics() {
    if (!patientInfo.name || !patientInfo.visitNumber || totalPlayTime === 0) {
        return;
    }

    try {
        const data = {
            patient_name: patientInfo.name,
            visit_number: patientInfo.visitNumber,
            play_duration: totalPlayTime,
            media_names: patientInfo.mediaNames,
            current_media: currentPlayingFile ? currentPlayingFile.name : '',
            timestamp: new Date().toISOString()
        };

        const response = await fetch(`${httpServerUrl}/api/report_play_statistics`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            console.log('播放统计上报成功:', data);
        } else {
            console.warn('播放统计上报失败:', response.status);
        }
    } catch (error) {
        console.warn('上报播放统计失败:', error.message);
    }
}

// ==================== 混合播放列表功能 ====================

// 加载任务媒体到混合播放列表（支持音频+视频混合显示）
function loadTaskMediaToMixedPlaylist(mediaNames) {
    console.log('🎵🎬 加载任务媒体到混合播放列表:', mediaNames);

    // 在媒体库中搜索匹配的文件
    const matchedFiles = [];

    for (const mediaName of mediaNames) {
        // 在音频和视频库中搜索
        for (const [type, categories] of Object.entries(mediaLibrary)) {
            for (const [categoryName, files] of Object.entries(categories)) {
                for (const file of files) {
                    // 模糊匹配文件名
                    if (file.name.toLowerCase().includes(mediaName.toLowerCase()) ||
                        mediaName.toLowerCase().includes(file.name.toLowerCase().replace(/\.[^/.]+$/, ""))) {
                        matchedFiles.push({
                            ...file,
                            categoryName: categoryName,
                            mediaType: type
                        });
                        console.log(`✅ 匹配到${type}文件: ${file.name} (分类: ${categoryName})`);
                        break;
                    }
                }
            }
        }
    }

    if (matchedFiles.length > 0) {
        // 设置为任务播放列表模式
        currentCategory = { type: 'task', categoryName: '任务播放列表' };
        currentFiles = matchedFiles;
        currentPage = 1;

        // 切换到任务播放列表视图
        switchToTaskPlaylistView();

        // 更新文件列表显示
        updateFileList();
        updatePagination();

        console.log(`📋 已加载 ${matchedFiles.length} 个文件到任务播放列表`);
        updateStatus(`已加载 ${matchedFiles.length} 个康复内容，包含音频和视频`);
    } else {
        console.warn('❌ 未找到匹配的媒体文件:', mediaNames);
        updateStatus('未找到指定的康复内容');
    }
}

// 切换到任务播放列表视图
function switchToTaskPlaylistView() {
    console.log('🔄 切换到任务播放列表视图');

    // 取消所有标签页的激活状态
    elements.audioTabBtn.classList.remove('active');
    elements.videoTabBtn.classList.remove('active');

    // 清除目录树的选中状态
    document.querySelectorAll('.tree-node.active').forEach(node => {
        node.classList.remove('active');
    });

    // 更新面包屑导航
    updateBreadcrumb();
}
