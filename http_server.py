#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
心理康复系统 HTTP API 服务器
用于接收平台下发信息和处理康复系统请求
"""

import json
import time
import threading
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import requests
import os
import logging

# MQTT支持
try:
    import paho.mqtt.client as mqtt
    MQTT_AVAILABLE = True
    print("[INFO] MQTT库已加载，支持MQTT协议")
except ImportError:
    MQTT_AVAILABLE = False
    print("[WARNING] MQTT库未安装，仅支持HTTP协议。如需MQTT支持，请运行: pip install paho-mqtt")

# 加载配置文件
def load_config():
    """加载配置文件"""
    config_file = 'config.json'
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"[SUCCESS] 配置文件加载成功: {config_file}")
        return config
    except FileNotFoundError:
        print(f"[ERROR] 配置文件不存在: {config_file}")
        # 返回默认配置
        return {
            "platform": {
                "api_base_url": "http://localhost:3000",
                "endpoints": {
                    "report_statistics": "/api/report_play_statistics",
                    "report_completion": "/api/report_task_completion"
                },
                "timeout": 10000
            },
            "httpserver": {
                "host": "0.0.0.0",
                "port": 8888,
                "endpoints": {
                    "receive_rehabilitation_info": "/api/receive_rehabilitation_info",
                    "current_visit": "/api/current_visit",
                    "report_play_statistics": "/api/report_play_statistics"
                }
            }
        }
    except Exception as e:
        print(f"[ERROR] 配置文件加载失败: {e}")
        return None

# 全局配置变量
CONFIG = load_config()

# 配置日志
def clean_old_logs():
    """清理超过7天的日志文件和目录"""
    from datetime import datetime, timedelta
    import os
    import shutil

    logs_dir = 'logs'
    if not os.path.exists(logs_dir):
        return

    # 计算7天前的日期
    cutoff_date = datetime.now() - timedelta(days=7)
    cutoff_date_str = cutoff_date.strftime('%Y-%m-%d')

    try:
        # 遍历logs目录下的所有子目录
        for item in os.listdir(logs_dir):
            item_path = os.path.join(logs_dir, item)

            # 只处理目录，且目录名符合日期格式
            if os.path.isdir(item_path) and len(item) == 10 and item.count('-') == 2:
                try:
                    # 验证是否为有效日期格式
                    item_date = datetime.strptime(item, '%Y-%m-%d')

                    # 如果日期超过7天，删除整个目录
                    if item_date < cutoff_date:
                        shutil.rmtree(item_path)
                        logger.info(f"已清理过期日志目录: {item}")

                except ValueError:
                    # 不是有效的日期格式，跳过
                    continue

    except Exception as e:
        logger.error(f"清理日志时发生错误: {e}")

def setup_logging():
    """设置日志配置，按日期创建目录和文件"""
    from datetime import datetime
    import os

    # 获取当前日期
    current_date = datetime.now().strftime('%Y-%m-%d')

    # 创建logs目录
    logs_dir = 'logs'
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)

    # 创建日期子目录
    date_dir = os.path.join(logs_dir, current_date)
    if not os.path.exists(date_dir):
        os.makedirs(date_dir)

    # 日志文件路径
    log_file = os.path.join(date_dir, 'log.txt')

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    return log_file

# 初始化日志
log_file_path = setup_logging()
logger = logging.getLogger(__name__)
logger.info(f"日志文件: {log_file_path}")

# 清理过期日志
clean_old_logs()

class RehabilitationConfig:
    """康复系统配置"""
    # 从配置文件加载配置
    if CONFIG:
        # 服务器配置
        SERVER_HOST = CONFIG.get('httpserver', {}).get('host', '0.0.0.0')
        SERVER_PORT = CONFIG.get('httpserver', {}).get('port', 8888)

        # 平台配置
        platform_config = CONFIG.get('platform', {})
        PLATFORM_SERVER = platform_config.get('api_base_url', 'http://platform-server:port')
        PRODUCT_ID = platform_config.get('product_id', 'rehabilitation_system')
        DEVICE_ID = platform_config.get('device_id', 'device_001')
        TOKEN = platform_config.get('token', 'your_bearer_token_here')
        TIMEOUT = platform_config.get('timeout', 10000) / 1000  # 转换为秒

        # API端点配置
        ENDPOINTS = platform_config.get('endpoints', {})

        # MQTT配置
        mqtt_config = CONFIG.get('mqtt', {})
        MQTT_ENABLED = mqtt_config.get('enabled', False) and MQTT_AVAILABLE
        MQTT_BROKER_HOST = mqtt_config.get('broker_host', 'localhost')
        MQTT_BROKER_PORT = mqtt_config.get('broker_port', 1883)
        MQTT_USERNAME = mqtt_config.get('username', '')
        MQTT_PASSWORD = mqtt_config.get('password', '')
        MQTT_CLIENT_ID = mqtt_config.get('client_id', 'rehabilitation_device_001')
        MQTT_TOPICS = mqtt_config.get('topics', {})
        MQTT_QOS = mqtt_config.get('qos', 1)
        MQTT_KEEP_ALIVE = mqtt_config.get('keep_alive', 60)
        MQTT_CLEAN_SESSION = mqtt_config.get('clean_session', True)
        MQTT_RECONNECT_DELAY = mqtt_config.get('reconnect_delay', 5)
    else:
        # 默认配置
        SERVER_HOST = '0.0.0.0'
        SERVER_PORT = 8888
        PLATFORM_SERVER = "http://platform-server:port"
        PRODUCT_ID = "rehabilitation_system"
        DEVICE_ID = "device_001"
        TOKEN = "your_bearer_token_here"
        TIMEOUT = 10
        ENDPOINTS = {}

        # MQTT默认配置
        MQTT_ENABLED = False
        MQTT_BROKER_HOST = 'localhost'
        MQTT_BROKER_PORT = 1883
        MQTT_USERNAME = ''
        MQTT_PASSWORD = ''
        MQTT_CLIENT_ID = 'rehabilitation_device_001'
        MQTT_TOPICS = {}
        MQTT_QOS = 1
        MQTT_KEEP_ALIVE = 60
        MQTT_CLEAN_SESSION = True
        MQTT_RECONNECT_DELAY = 5
    

    
    # 临时文件路径
    TEMP_DATA_FILE = 'temp_rehabilitation_data.json'
    PLAY_STATS_FILE = 'play_statistics.json'

class RehabilitationData:
    """康复数据管理"""
    
    @staticmethod
    def save_visit_info(data):
        """保存就诊信息到临时文件"""
        try:
            # 添加接收时间
            data['receivedTime'] = datetime.now().isoformat()
            
            # 读取现有数据
            existing_data = []
            if os.path.exists(RehabilitationConfig.TEMP_DATA_FILE):
                with open(RehabilitationConfig.TEMP_DATA_FILE, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            
            # 添加新数据
            existing_data.append(data)
            
            # 保存到文件
            with open(RehabilitationConfig.TEMP_DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"保存就诊信息成功: visitId={data.get('visitId')}")
            return True
        except Exception as e:
            logger.error(f"保存就诊信息失败: {e}")
            return False
    
    @staticmethod
    def get_current_visit():
        """获取当前就诊信息"""
        try:
            if os.path.exists(RehabilitationConfig.TEMP_DATA_FILE):
                with open(RehabilitationConfig.TEMP_DATA_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if data:
                        return data[-1]  # 返回最新的就诊信息
            return None
        except Exception as e:
            logger.error(f"读取就诊信息失败: {e}")
            return None
    
    @staticmethod
    def save_play_statistics(visit_id, item_code, play_duration):
        """保存播放统计"""
        try:
            stats = {
                'visitId': visit_id,
                'itemCode': item_code,
                'playDuration': play_duration,
                'timestamp': datetime.now().isoformat()
            }
            
            # 读取现有统计
            existing_stats = []
            if os.path.exists(RehabilitationConfig.PLAY_STATS_FILE):
                with open(RehabilitationConfig.PLAY_STATS_FILE, 'r', encoding='utf-8') as f:
                    existing_stats = json.load(f)
            
            # 添加新统计
            existing_stats.append(stats)
            
            # 保存到文件
            with open(RehabilitationConfig.PLAY_STATS_FILE, 'w', encoding='utf-8') as f:
                json.dump(existing_stats, f, ensure_ascii=False, indent=2)
            
            logger.info(f"保存播放统计成功: visitId={visit_id}, duration={play_duration}s")
            return True
        except Exception as e:
            logger.error(f"保存播放统计失败: {e}")
            return False

class PlatformAPI:
    """平台接口调用"""
    
    @staticmethod
    def get_headers():
        """获取请求头"""
        return {
            'Authorization': f'Bearer {RehabilitationConfig.TOKEN}',
            'Content-Type': 'application/json'
        }
    
    @staticmethod
    def report_properties(properties):
        """上报设备属性"""
        # 使用标准格式：/{productId}/{deviceId}/properties/report
        endpoint = f'/{RehabilitationConfig.PRODUCT_ID}/{RehabilitationConfig.DEVICE_ID}/properties/report'
        url = f"{RehabilitationConfig.PLATFORM_SERVER}{endpoint}"

        # 记录完整URL日志
        logger.info(f"上报设备属性 - 完整URL: {url}")
        logger.info(f"请求方法: POST, 端点: {endpoint}")

        try:
            headers = PlatformAPI.get_headers()

            # 记录请求详情
            logger.info(f"请求头: {headers}")
            logger.info(f"请求体: {properties}")

            response = requests.post(url, json=properties, headers=headers, timeout=RehabilitationConfig.TIMEOUT)

            # 记录响应详情
            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")

            if response.status_code == 200:
                logger.info("设备属性上报成功")
                return True
            else:
                logger.error(f"设备属性上报失败: {response.status_code}, 响应: {response.text}")
                return False
        except Exception as e:
            logger.error(f"设备属性上报异常: {e}")
            logger.error(f"请求URL: {url}")
            return False
    
    @staticmethod
    def report_event(event_id, event_data):
        """上报设备事件"""
        # 使用标准格式：/{productId}/{deviceId}/event/{event_id}
        endpoint = f'/{RehabilitationConfig.PRODUCT_ID}/{RehabilitationConfig.DEVICE_ID}/event/{event_id}'
        url = f"{RehabilitationConfig.PLATFORM_SERVER}{endpoint}"

        # 记录完整URL日志
        logger.info(f"上报设备事件 - 完整URL: {url}")
        logger.info(f"请求方法: POST, 端点: {endpoint}, 事件ID: {event_id}")

        try:
            headers = PlatformAPI.get_headers()

            # 记录请求详情
            logger.info(f"请求头: {headers}")
            logger.info(f"请求体: {event_data}")

            response = requests.post(url, json=event_data, headers=headers, timeout=RehabilitationConfig.TIMEOUT)

            # 记录响应详情
            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")

            if response.status_code == 200:
                logger.info(f"设备事件上报成功: {event_id}")
                return True
            else:
                logger.error(f"设备事件上报失败: {response.status_code}, 响应: {response.text}")
                return False
        except Exception as e:
            logger.error(f"设备事件上报异常: {e}")
            logger.error(f"请求URL: {url}")
            return False
    
    @staticmethod
    def send_heartbeat():
        """发送心跳"""
        # 使用标准格式：/{productId}/{deviceId}/heartbeat
        endpoint = f'/{RehabilitationConfig.PRODUCT_ID}/{RehabilitationConfig.DEVICE_ID}/heartbeat'
        url = f"{RehabilitationConfig.PLATFORM_SERVER}{endpoint}"

        # 记录完整URL日志
        logger.info(f"发送心跳请求 - 完整URL: {url}")
        logger.info(f"请求方法: POST, 端点: {endpoint}")

        try:
            payload = {'timestamp': datetime.now().isoformat()}
            headers = PlatformAPI.get_headers()

            # 记录请求详情
            logger.info(f"请求头: {headers}")
            logger.info(f"请求体: {payload}")

            response = requests.post(url, json=payload, headers=headers, timeout=RehabilitationConfig.TIMEOUT)

            # 记录响应详情
            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")

            if response.status_code == 200:
                logger.info("心跳发送成功")
                return True
            else:
                logger.error(f"心跳发送失败: {response.status_code}, 响应: {response.text}")
                return False
        except Exception as e:
            logger.error(f"心跳发送异常: {e}")
            logger.error(f"请求URL: {url}")
            return False

    @staticmethod
    def report_statistics(stats_data):
        """上报播放统计"""
        # 使用标准格式：/{productId}/{deviceId}/statistics
        endpoint = f'/{RehabilitationConfig.PRODUCT_ID}/{RehabilitationConfig.DEVICE_ID}/statistics'
        url = f"{RehabilitationConfig.PLATFORM_SERVER}{endpoint}"

        # 记录完整URL日志
        logger.info(f"上报播放统计 - 完整URL: {url}")
        logger.info(f"请求方法: POST, 端点: {endpoint}")

        try:
            headers = PlatformAPI.get_headers()

            # 记录请求详情
            logger.info(f"请求头: {headers}")
            logger.info(f"请求体: {stats_data}")

            response = requests.post(url, json=stats_data, headers=headers, timeout=RehabilitationConfig.TIMEOUT)

            # 记录响应详情
            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")

            if response.status_code == 200:
                logger.info("播放统计上报成功")
                return True
            else:
                logger.error(f"播放统计上报失败: {response.status_code}, 响应: {response.text}")
                return False
        except Exception as e:
            logger.error(f"播放统计上报异常: {e}")
            logger.error(f"请求URL: {url}")
            return False

class MQTTClient:
    """MQTT客户端管理"""

    def __init__(self):
        self.client = None
        self.connected = False
        self.reconnect_timer = None

    def on_connect(self, client, userdata, flags, rc):
        """连接回调"""
        if rc == 0:
            self.connected = True
            logger.info(f"MQTT连接成功: {RehabilitationConfig.MQTT_BROKER_HOST}:{RehabilitationConfig.MQTT_BROKER_PORT}")

            # 订阅接收康复信息的主题
            receive_topic = RehabilitationConfig.MQTT_TOPICS.get('receive_rehabilitation_info', '')
            if receive_topic:
                client.subscribe(receive_topic, RehabilitationConfig.MQTT_QOS)
                logger.info(f"订阅主题: {receive_topic}")

        else:
            self.connected = False
            logger.error(f"MQTT连接失败，错误码: {rc}")
            self.schedule_reconnect()

    def on_disconnect(self, client, userdata, rc):
        """断开连接回调"""
        self.connected = False
        logger.warning(f"MQTT连接断开，错误码: {rc}")
        if rc != 0:
            self.schedule_reconnect()

    def on_message(self, client, userdata, msg):
        """消息接收回调"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            logger.info(f"收到MQTT消息 - 主题: {topic}, 内容: {payload}")

            # 解析JSON数据
            try:
                data = json.loads(payload)
            except json.JSONDecodeError as e:
                logger.error(f"MQTT消息JSON解析失败: {e}")
                return

            # 根据主题处理不同类型的消息
            receive_topic = RehabilitationConfig.MQTT_TOPICS.get('receive_rehabilitation_info', '')

            if topic == receive_topic:
                # 处理康复信息，使用与HTTP相同的处理逻辑
                self.handle_mqtt_rehabilitation_info(data)
            else:
                logger.warning(f"未知的MQTT主题: {topic}")

        except Exception as e:
            logger.error(f"处理MQTT消息异常: {e}")

    def handle_mqtt_rehabilitation_info(self, data):
        """处理MQTT接收的康复信息"""
        try:
            # 验证必要字段 - 与HTTP处理逻辑相同
            if 'basic_info' not in data or 'training_task' not in data:
                logger.error("MQTT消息缺少必要字段: basic_info, training_task")
                return

            basic_info = data.get('basic_info', {})
            if not basic_info.get('id') or not basic_info.get('name'):
                logger.error("MQTT消息basic_info缺少必要字段: id, name")
                return

            # 保存就诊信息 - 使用相同的数据处理逻辑
            if RehabilitationData.save_visit_info(data):
                logger.info(f"MQTT康复信息保存成功: patient_id={basic_info.get('id')}, patient_name={basic_info.get('name')}")

                # 上报设备事件
                event_data = {
                    'eventType': 'music_video_therapy_received_mqtt',
                    'patient_id': basic_info.get('id'),
                    'patient_name': basic_info.get('name'),
                    'training_project': data.get('training_task', {}).get('training_project', ''),
                    'timestamp': datetime.now().isoformat(),
                    'source': 'mqtt'
                }
                PlatformAPI.report_event('therapy_received', event_data)
            else:
                logger.error("MQTT康复信息保存失败")

        except Exception as e:
            logger.error(f"处理MQTT康复信息异常: {e}")

    def schedule_reconnect(self):
        """安排重连"""
        if self.reconnect_timer:
            self.reconnect_timer.cancel()

        def reconnect():
            logger.info(f"尝试重新连接MQTT服务器...")
            self.connect()

        self.reconnect_timer = threading.Timer(RehabilitationConfig.MQTT_RECONNECT_DELAY, reconnect)
        self.reconnect_timer.start()

    def connect(self):
        """连接MQTT服务器"""
        if not MQTT_AVAILABLE:
            logger.warning("MQTT库未安装，无法连接MQTT服务器")
            return False

        if not RehabilitationConfig.MQTT_ENABLED:
            logger.info("MQTT功能已禁用")
            return False

        try:
            # 创建MQTT客户端
            self.client = mqtt.Client(
                client_id=RehabilitationConfig.MQTT_CLIENT_ID,
                clean_session=RehabilitationConfig.MQTT_CLEAN_SESSION
            )

            # 设置回调函数
            self.client.on_connect = self.on_connect
            self.client.on_disconnect = self.on_disconnect
            self.client.on_message = self.on_message

            # 设置用户名和密码（如果有）
            if RehabilitationConfig.MQTT_USERNAME:
                self.client.username_pw_set(
                    RehabilitationConfig.MQTT_USERNAME,
                    RehabilitationConfig.MQTT_PASSWORD
                )

            # 连接到MQTT服务器
            logger.info(f"正在连接MQTT服务器: {RehabilitationConfig.MQTT_BROKER_HOST}:{RehabilitationConfig.MQTT_BROKER_PORT}")
            self.client.connect(
                RehabilitationConfig.MQTT_BROKER_HOST,
                RehabilitationConfig.MQTT_BROKER_PORT,
                RehabilitationConfig.MQTT_KEEP_ALIVE
            )

            # 启动网络循环
            self.client.loop_start()
            return True

        except Exception as e:
            logger.error(f"MQTT连接异常: {e}")
            return False

    def disconnect(self):
        """断开MQTT连接"""
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()
            self.connected = False
            logger.info("MQTT连接已断开")

    def publish_heartbeat(self):
        """发布MQTT心跳"""
        if not self.connected:
            return False

        heartbeat_topic = RehabilitationConfig.MQTT_TOPICS.get('heartbeat', '')
        if not heartbeat_topic:
            return False

        try:
            payload = {
                'device_id': RehabilitationConfig.DEVICE_ID,
                'timestamp': datetime.now().isoformat(),
                'status': 'online'
            }

            result = self.client.publish(
                heartbeat_topic,
                json.dumps(payload, ensure_ascii=False),
                RehabilitationConfig.MQTT_QOS
            )

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"MQTT心跳发送成功: {heartbeat_topic}")
                return True
            else:
                logger.error(f"MQTT心跳发送失败: {result.rc}")
                return False

        except Exception as e:
            logger.error(f"MQTT心跳发送异常: {e}")
            return False

# 全局MQTT客户端实例
mqtt_client = MQTTClient() if MQTT_AVAILABLE else None

class RehabilitationHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""
    
    def log_message(self, format, *args):
        """重写日志方法"""
        logger.info(f"{self.address_string()} - {format % args}")
    
    def do_POST(self):
        """处理POST请求"""
        try:
            # 解析URL路径
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            
            # 读取请求体
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            if content_length > 0:
                try:
                    request_data = json.loads(post_data.decode('utf-8'))
                except json.JSONDecodeError:
                    self.send_error_response(400, "Invalid JSON format")
                    return
            else:
                request_data = {}
            
            # 路由处理
            if path == '/api/receive_rehabilitation_info':
                self.handle_receive_rehabilitation_info(request_data)
            elif path == '/api/report_play_statistics':
                self.handle_report_play_statistics(request_data)
            else:
                self.send_error_response(404, "API not found")
                
        except Exception as e:
            logger.error(f"处理POST请求异常: {e}")
            self.send_error_response(500, "Internal server error")
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            
            if path == '/api/status':
                self.handle_status()
            elif path == '/api/current_visit':
                self.handle_current_visit()
            else:
                self.send_error_response(404, "API not found")
                
        except Exception as e:
            logger.error(f"处理GET请求异常: {e}")
            self.send_error_response(500, "Internal server error")
    
    def handle_receive_rehabilitation_info(self, data):
        """处理接收康复信息 - 支持音乐视频疗法结构化参数"""
        try:
            # 验证必要字段 - 根据新的结构化参数
            if 'basic_info' not in data or 'training_task' not in data:
                self.send_error_response(400, "Missing required fields: basic_info, training_task")
                return

            basic_info = data.get('basic_info', {})
            if not basic_info.get('id') or not basic_info.get('name'):
                self.send_error_response(400, "Missing required fields in basic_info: id, name")
                return

            # 保存就诊信息
            if RehabilitationData.save_visit_info(data):
                response = {
                    'success': True,
                    'message': '音乐视频疗法信息接收成功',
                    'patient_id': basic_info.get('id'),
                    'patient_name': basic_info.get('name')
                }
                self.send_json_response(200, response)

                # 上报设备事件
                event_data = {
                    'eventType': 'music_video_therapy_received',
                    'patient_id': basic_info.get('id'),
                    'patient_name': basic_info.get('name'),
                    'training_project': data.get('training_task', {}).get('training_project', ''),
                    'timestamp': datetime.now().isoformat()
                }
                PlatformAPI.report_event('therapy_received', event_data)
            else:
                self.send_error_response(500, "Failed to save therapy info")

        except Exception as e:
            logger.error(f"处理音乐视频疗法信息异常: {e}")
            self.send_error_response(500, "Internal server error")
    

    
    def handle_report_play_statistics(self, data):
        """处理播放统计上报 - 按指定JSON格式上报"""
        try:
            # 解析必要字段
            patient_name = data.get('patient_name')
            visit_number = data.get('visit_number')
            play_duration = data.get('play_duration')
            media_names = data.get('media_names', [])
            current_media = data.get('current_media', '')
            company = data.get('company', '海南艾谱特医疗科技有限公司')
            report_date = data.get('report_date', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

            # headers相关字段
            headers = data.get('headers', {})
            # 兼容旧参数
            if not headers:
                headers = {
                    "visitId": data.get("visit_id", ""),
                    "patientId": data.get("patient_id", ""),
                    "visitNo": visit_number or "",
                    "itemId": data.get("item_id", 0),
                    "itemType": data.get("item_type", 1)
                }

            # basic_info
            basic_info = data.get('basic_info', {})
            if not basic_info:
                basic_info = {
                    "name": patient_name or "",
                    "department": data.get("department", ""),
                    "gender": data.get("gender", 1)
                }

            # training_task
            training_task = data.get('training_task', {})
            if not training_task:
                training_task = {
                    "title": current_media or "",
                    "media_type": data.get("media_type", ""),
                    "title_type": data.get("title_type", ""),
                    "every_day_num": data.get("every_day_num", 1),
                    "day_num": data.get("day_num", 1)
                }

            # training_effect
            training_effect = data.get('training_effect')
            if not training_effect:
                # 构造一个简单的示例
                today = datetime.now().strftime('%Y-%m-%d')
                training_effect = [{
                    "date": today,
                    "effect": {
                        "not_start_count": 0,
                        "under_way_count": 0,
                        "finish_count": 1,
                        "use_time": play_duration or 0,
                        "total_time": play_duration or 0
                    }
                }]

            # 构建上报数据
            properties = {
                "headers": headers,
                "company": company,
                "basic_info": basic_info,
                "training_task": training_task,
                "training_effect": training_effect,
                "report_date": report_date
            }

            # 保存本地统计
            RehabilitationData.save_play_statistics(visit_number, current_media, play_duration)

            # 上报到平台（包裹为 {"properties": ...}）
            report_payload = {"properties": properties}
            success = PlatformAPI.report_statistics(report_payload)

            response = {
                'success': True,
                'message': '音乐视频疗法统计上报成功',
                'platformReported': success,
                'report_payload': report_payload
            }
            self.send_json_response(200, response)

        except Exception as e:
            logger.error(f"播放统计上报异常: {e}")
            self.send_error_response(500, "Internal server error")
    
    def handle_status(self):
        """处理状态查询"""
        try:
            status = {
                'server': 'running',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            }
            self.send_json_response(200, status)
        except Exception as e:
            logger.error(f"状态查询异常: {e}")
            self.send_error_response(500, "Internal server error")
    
    def handle_current_visit(self):
        """处理当前就诊信息查询 - 返回适配前端的格式"""
        try:
            current_visit = RehabilitationData.get_current_visit()
            if current_visit:
                # 转换为前端期望的格式
                basic_info = current_visit.get('basic_info', {})
                training_task = current_visit.get('training_task', {})

                # 构建康复内容列表
                rehabilitation_content = []
                training_project = training_task.get('training_project', '')
                if training_project:
                    rehabilitation_content.append(training_project)

                response = {
                    'patient_name': basic_info.get('name', ''),
                    'visit_number': basic_info.get('id', ''),
                    'rehabilitation_content': rehabilitation_content,
                    'department': basic_info.get('department', ''),
                    'gender': basic_info.get('gender', ''),
                    'age': basic_info.get('age', 0),
                    'training_duration': training_task.get('training_duration', 0),
                    'training_frequency': training_task.get('training_frequency', 0),
                    'company': current_visit.get('company', ''),
                    'assessment': current_visit.get('assessment', ''),
                    'raw_data': current_visit  # 保留原始数据供调试
                }
                self.send_json_response(200, response)
            else:
                self.send_json_response(200, {
                    'patient_name': '',
                    'visit_number': '',
                    'rehabilitation_content': [],
                    'message': 'No current visit'
                })
        except Exception as e:
            logger.error(f"查询当前就诊信息异常: {e}")
            self.send_error_response(500, "Internal server error")
    
    def send_json_response(self, status_code, data):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        response_json = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def send_error_response(self, status_code, message):
        """发送错误响应"""
        error_data = {
            'success': False,
            'error': message,
            'timestamp': datetime.now().isoformat()
        }
        self.send_json_response(status_code, error_data)

def heartbeat_worker():
    """心跳工作线程 - 支持HTTP和MQTT双协议"""
    while True:
        try:
            # HTTP心跳
            PlatformAPI.send_heartbeat()

            # MQTT心跳
            if mqtt_client and RehabilitationConfig.MQTT_ENABLED:
                mqtt_client.publish_heartbeat()

            time.sleep(60)  # 每分钟发送一次心跳
        except Exception as e:
            logger.error(f"心跳线程异常: {e}")
            time.sleep(60)

def main():
    """主函数 - 支持HTTP和MQTT双协议"""
    try:
        # 输出配置信息
        logger.info("=== 康复系统配置信息 ===")
        logger.info(f"平台服务器: {RehabilitationConfig.PLATFORM_SERVER}")
        logger.info(f"产品ID: {RehabilitationConfig.PRODUCT_ID}")
        logger.info(f"设备ID: {RehabilitationConfig.DEVICE_ID}")
        logger.info(f"认证Token: {RehabilitationConfig.TOKEN[:20]}...")
        logger.info(f"请求超时: {RehabilitationConfig.TIMEOUT}秒")
        logger.info("=== 平台API端点格式 ===")
        logger.info(f"心跳接口: {RehabilitationConfig.PLATFORM_SERVER}/{RehabilitationConfig.PRODUCT_ID}/{RehabilitationConfig.DEVICE_ID}/heartbeat")
        logger.info(f"事件上报: {RehabilitationConfig.PLATFORM_SERVER}/{RehabilitationConfig.PRODUCT_ID}/{RehabilitationConfig.DEVICE_ID}/event/{{event_id}}")
        logger.info(f"属性上报: {RehabilitationConfig.PLATFORM_SERVER}/{RehabilitationConfig.PRODUCT_ID}/{RehabilitationConfig.DEVICE_ID}/properties/report")
        logger.info(f"统计上报: {RehabilitationConfig.PLATFORM_SERVER}/{RehabilitationConfig.PRODUCT_ID}/{RehabilitationConfig.DEVICE_ID}/statistics")

        # 输出MQTT配置信息
        logger.info("=== MQTT配置信息 ===")
        if RehabilitationConfig.MQTT_ENABLED:
            logger.info(f"MQTT状态: 已启用")
            logger.info(f"MQTT服务器: {RehabilitationConfig.MQTT_BROKER_HOST}:{RehabilitationConfig.MQTT_BROKER_PORT}")
            logger.info(f"客户端ID: {RehabilitationConfig.MQTT_CLIENT_ID}")
            logger.info(f"订阅主题: {RehabilitationConfig.MQTT_TOPICS}")
        else:
            logger.info(f"MQTT状态: 已禁用")
        logger.info("========================")

        # 启动MQTT客户端
        if mqtt_client and RehabilitationConfig.MQTT_ENABLED:
            logger.info("正在启动MQTT客户端...")
            if mqtt_client.connect():
                logger.info("MQTT客户端启动成功")
            else:
                logger.warning("MQTT客户端启动失败，仅使用HTTP协议")

        # 启动心跳线程
        heartbeat_thread = threading.Thread(target=heartbeat_worker, daemon=True)
        heartbeat_thread.start()
        logger.info("心跳线程已启动")

        # 启动HTTP服务器
        server_address = (RehabilitationConfig.SERVER_HOST, RehabilitationConfig.SERVER_PORT)
        httpd = HTTPServer(server_address, RehabilitationHandler)

        logger.info(f"康复系统HTTP服务器启动成功")
        logger.info(f"监听地址: http://{RehabilitationConfig.SERVER_HOST}:{RehabilitationConfig.SERVER_PORT}")
        logger.info("=== 支持的协议和接口 ===")
        logger.info("HTTP API接口:")
        logger.info("  POST /api/receive_rehabilitation_info - 接收康复信息")
        logger.info("  POST /api/report_play_statistics - 上报播放统计")
        logger.info("  GET  /api/status - 服务器状态")
        logger.info("  GET  /api/current_visit - 当前就诊信息")

        if RehabilitationConfig.MQTT_ENABLED:
            logger.info("MQTT订阅主题:")
            for topic_name, topic_path in RehabilitationConfig.MQTT_TOPICS.items():
                logger.info(f"  {topic_name}: {topic_path}")

        logger.info("========================")
        logger.info("服务器已启动，同时支持HTTP和MQTT协议接收数据")

        httpd.serve_forever()

    except KeyboardInterrupt:
        logger.info("服务器正在关闭...")
        # 关闭MQTT连接
        if mqtt_client:
            mqtt_client.disconnect()
        httpd.shutdown()
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        # 关闭MQTT连接
        if mqtt_client:
            mqtt_client.disconnect()

if __name__ == '__main__':
    main()
