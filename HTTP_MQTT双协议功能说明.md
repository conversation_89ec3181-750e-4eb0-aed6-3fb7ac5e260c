# HTTP/MQTT双协议功能说明 v2.0

## 📋 功能概述

康复系统HTTP服务器现已升级支持HTTP和MQTT双协议，完全符合平台标准MQTT协议v2.0规范，可以同时监听两种协议的数据接收，实现更灵活的平台集成方案。

## 🔧 主要特性

### ✅ 双协议支持
- **HTTP协议**: 传统的REST API接口，适合同步调用
- **MQTT协议**: 轻量级消息队列协议，适合实时推送和异步通信

### ✅ 统一数据处理
- 无论通过HTTP还是MQTT接收的数据，都使用相同的处理逻辑
- 数据验证、保存、上报流程完全一致
- 保证数据处理的一致性和可靠性

### ✅ 灵活配置
- 可通过配置文件启用/禁用MQTT功能
- 支持MQTT服务器认证配置
- 可自定义MQTT主题和QoS等级

### ✅ 向后兼容
- 完全兼容现有的HTTP接口
- 不影响现有功能和集成方式
- 可以渐进式升级到双协议模式

## 🚀 使用场景

### HTTP协议适用场景
- 主动查询数据
- 同步数据交互
- 传统Web服务集成
- 调试和测试

### MQTT协议适用场景
- 实时数据推送
- 异步消息通信
- 物联网设备集成
- 高并发场景

## ⚙️ 配置说明

### 启用MQTT功能（符合平台标准v2.0）
在 `config.json` 中设置：
```json
{
  "mqtt": {
    "enabled": true,
    "broker_host": "*************",
    "broker_port": 1883,
    "auth": {
      "secure_id": "rehabilitation_device_001",
      "secure_key": "your_secure_key_here",
      "client_id": "rehabilitation_device_001"
    },
    "device": {
      "product_id": "rehabilitation_system",
      "device_id": "device_001"
    },
    "topics": {
      "properties_read": "/{productId}/{deviceId}/properties/read",
      "function_invoke": "/{productId}/{deviceId}/function/invoke",
      "properties_report": "/{productId}/{deviceId}/properties/report"
    }
  }
}
```

### 禁用MQTT功能
```json
{
  "mqtt": {
    "enabled": false
  }
}
```

## 📡 接口对比

### HTTP接口
```
POST http://localhost:8888/api/receive_rehabilitation_info
Content-Type: application/json

{康复信息JSON数据}
```

### MQTT接口（平台标准v2.0）

#### 1. 功能调用（平台→设备）
```
主题: /rehabilitation_system/device_001/function/invoke
QoS: 1
消息体: {
  "timestamp": 1601196762389,
  "messageId": "msg_001",
  "deviceId": "device_001",
  "functionId": "start_rehabilitation",
  "headers": {
    "visitId": "43a962b625eb4025a7d70a39a5bf6a2b",
    "patientId": "3586d330b4df4e8b8a9cf43bbda0b6c7",
    "visitNo": "202401010001",
    "itemId": "69bfcd0184cb464eb5c2aed22eb653a4",
    "itemType": "1"
  },
  "inputs": [
    {
      "name": "rehabilitation_info",
      "value": "{康复信息JSON数据}"
    }
  ]
}
```

#### 2. 属性上报（设备→平台）
```
主题: /rehabilitation_system/device_001/properties/report
QoS: 1
消息体: {
  "messageId": "report_001",
  "deviceId": "device_001",
  "headers": {
    "visitId": "43a962b625eb4025a7d70a39a5bf6a2b",
    "patientId": "3586d330b4df4e8b8a9cf43bbda0b6c7"
  },
  "properties": {
    "status": "online",
    "timestamp": 1601196762389
  }
}
```

#### 3. 事件上报（设备→平台）
```
主题: /rehabilitation_system/device_001/event/therapy_received
QoS: 1
消息体: {
  "timestamp": 1601196762389,
  "messageId": "event_001",
  "headers": {
    "visitId": "43a962b625eb4025a7d70a39a5bf6a2b",
    "patientId": "3586d330b4df4e8b8a9cf43bbda0b6c7"
  },
  "data": {
    "eventType": "therapy_received",
    "patient_id": "20240616001",
    "patient_name": "李四"
  }
}
```

## 🔄 数据流程

1. **数据接收**
   - HTTP: 通过POST请求接收
   - MQTT: 通过订阅主题接收

2. **数据验证**
   - 统一的JSON格式验证
   - 必要字段检查

3. **数据保存**
   - 保存到本地临时文件
   - 添加接收时间戳

4. **事件上报**
   - 向平台上报设备事件
   - 记录数据来源（HTTP/MQTT）

## 🛠️ 安装和部署

### 1. 安装MQTT依赖
```bash
pip install paho-mqtt
```

或运行提供的安装脚本：
```bash
python install_mqtt.py
```

### 2. 配置MQTT参数
编辑 `config.json` 文件，设置MQTT服务器信息

### 3. 启动服务器
```bash
python http_server.py
```

或使用编译后的可执行文件：
```bash
httpServer.exe
```

## 🧪 测试工具

### HTTP协议测试
```bash
python test_platform_data.py
```

### MQTT协议测试
```bash
python test_mqtt_client.py
```

### 双协议综合测试
```bash
python test_dual_protocol.py
```

## 📊 监控和日志

### 启动日志示例
```
=== 康复系统配置信息 ===
平台服务器: http://*************
=== MQTT配置信息 ===
MQTT状态: 已启用
MQTT服务器: *************:1883
订阅主题: {'receive_rehabilitation_info': 'rehabilitation/device_001/receive_info'}
========================
MQTT连接成功: *************:1883
订阅主题: rehabilitation/device_001/receive_info
康复系统HTTP服务器启动成功
监听地址: http://0.0.0.0:8888
服务器已启动，同时支持HTTP和MQTT协议接收数据
```

### 数据接收日志
```
# HTTP数据接收
保存就诊信息成功: visitId=20240616001

# MQTT数据接收
收到MQTT消息 - 主题: rehabilitation/device_001/receive_info
MQTT康复信息保存成功: patient_id=20240616002
```

## 🔧 故障排除

### MQTT连接失败
1. 检查MQTT服务器地址和端口
2. 确认网络连接正常
3. 检查防火墙设置
4. 验证认证信息（如果需要）

### MQTT库未安装
```
[WARNING] MQTT库未安装，仅支持HTTP协议。如需MQTT支持，请运行: pip install paho-mqtt
```
解决方案：安装paho-mqtt库

### 配置文件错误
检查config.json格式是否正确，特别是MQTT配置部分

## 📈 性能优势

### 资源使用
- HTTP和MQTT共享同一进程
- 统一的数据处理逻辑，减少代码重复
- 智能重连机制，提高可靠性

### 扩展性
- 支持多种协议接入
- 易于添加新的消息队列协议
- 模块化设计，便于维护

## 🎯 最佳实践

1. **生产环境建议**
   - 启用MQTT的clean_session=false以保证消息可靠性
   - 设置合适的keep_alive时间
   - 配置MQTT服务器认证

2. **开发测试建议**
   - 使用HTTP接口进行调试
   - 使用MQTT接口测试实时性
   - 同时测试两种协议的数据一致性

3. **监控建议**
   - 监控MQTT连接状态
   - 记录数据来源统计
   - 设置告警机制

## 📞 技术支持

如有问题，请检查：
1. 日志文件中的错误信息
2. 网络连接状态
3. 配置文件格式
4. 依赖库安装情况

---

**版本**: v2.0.0  
**更新日期**: 2024-06-16  
**兼容性**: 向后兼容v1.x版本
