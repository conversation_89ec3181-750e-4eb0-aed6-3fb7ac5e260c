2025-06-16 15:13:49,017 - INFO - 日志文件: logs\2025-06-16\log.txt
2025-06-16 15:13:49,020 - INFO - === 康复系统配置信息 ===
2025-06-16 15:13:49,020 - INFO - 平台服务器: http://**************
2025-06-16 15:13:49,020 - INFO - 产品ID: 1859128349437280256
2025-06-16 15:13:49,020 - INFO - 设备ID: SN20250605
2025-06-16 15:13:49,020 - INFO - 认证Token: 1859128349437280256...
2025-06-16 15:13:49,020 - INFO - 请求超时: 10.0秒
2025-06-16 15:13:49,020 - INFO - === 平台API端点格式 ===
2025-06-16 15:13:49,021 - INFO - 心跳接口: http://**************/1859128349437280256/SN20250605/heartbeat
2025-06-16 15:13:49,021 - INFO - 事件上报: http://**************/1859128349437280256/SN20250605/event/{event_id}
2025-06-16 15:13:49,024 - INFO - 属性上报: http://**************/1859128349437280256/SN20250605/properties/report
2025-06-16 15:13:49,024 - INFO - 统计上报: http://**************/1859128349437280256/SN20250605/statistics
2025-06-16 15:13:49,025 - INFO - === MQTT配置信息 ===
2025-06-16 15:13:49,025 - INFO - MQTT状态: 已启用
2025-06-16 15:13:49,025 - INFO - MQTT服务器: **************:1883
2025-06-16 15:13:49,025 - INFO - 客户端ID: SN20250605
2025-06-16 15:13:49,025 - INFO - 订阅主题: {'properties_read': '/{productId}/{deviceId}/properties/read', 'properties_write': '/{productId}/{deviceId}/properties/write', 'function_invoke': '/{productId}/{deviceId}/function/invoke', 'properties_report': '/{productId}/{deviceId}/properties/report', 'properties_report_reply': '/{productId}/{deviceId}/properties/report/reply', 'event_report': '/{productId}/{deviceId}/event/{eventId}', 'event_report_reply': '/{productId}/{deviceId}/event/{eventId}/reply'}
2025-06-16 15:13:49,025 - INFO - ========================
2025-06-16 15:13:49,025 - INFO - 正在启动MQTT客户端...
2025-06-16 15:13:49,026 - INFO - MQTT认证: username=1859128349437280256|1750058029026
2025-06-16 15:13:49,026 - INFO - 正在连接MQTT服务器: **************:1883
2025-06-16 15:13:54,028 - ERROR - MQTT连接异常: timed out
2025-06-16 15:13:54,028 - WARNING - MQTT客户端启动失败，仅使用HTTP协议
2025-06-16 15:13:54,029 - INFO - 发送心跳请求 - 完整URL: http://**************/1859128349437280256/SN20250605/heartbeat
2025-06-16 15:13:54,030 - INFO - 心跳线程已启动
2025-06-16 15:13:54,030 - INFO - 请求方法: POST, 端点: /1859128349437280256/SN20250605/heartbeat
2025-06-16 15:13:54,030 - INFO - 请求头: {'Authorization': 'Bearer 1859128349437280256', 'Content-Type': 'application/json'}
2025-06-16 15:13:54,030 - INFO - 请求体: {'timestamp': '2025-06-16T15:13:54.030874'}
2025-06-16 15:13:55,399 - INFO - 康复系统HTTP服务器启动成功
2025-06-16 15:13:55,400 - INFO - 监听地址: http://0.0.0.0:8888
2025-06-16 15:13:55,400 - INFO - === 支持的协议和接口 ===
2025-06-16 15:13:55,400 - INFO - HTTP API接口:
2025-06-16 15:13:55,400 - INFO -   POST /api/receive_rehabilitation_info - 接收康复信息
2025-06-16 15:13:55,400 - INFO -   POST /api/report_play_statistics - 上报播放统计
2025-06-16 15:13:55,400 - INFO -   GET  /api/status - 服务器状态
2025-06-16 15:13:55,400 - INFO -   GET  /api/current_visit - 当前就诊信息
2025-06-16 15:13:55,400 - INFO - MQTT订阅主题:
2025-06-16 15:13:55,400 - INFO -   properties_read: /{productId}/{deviceId}/properties/read
2025-06-16 15:13:55,401 - INFO -   properties_write: /{productId}/{deviceId}/properties/write
2025-06-16 15:13:55,401 - INFO -   function_invoke: /{productId}/{deviceId}/function/invoke
2025-06-16 15:13:55,401 - INFO -   properties_report: /{productId}/{deviceId}/properties/report
2025-06-16 15:13:55,401 - INFO -   properties_report_reply: /{productId}/{deviceId}/properties/report/reply
2025-06-16 15:13:55,401 - INFO -   event_report: /{productId}/{deviceId}/event/{eventId}
2025-06-16 15:13:55,401 - INFO -   event_report_reply: /{productId}/{deviceId}/event/{eventId}/reply
2025-06-16 15:13:55,401 - INFO - ========================
2025-06-16 15:13:55,401 - INFO - 服务器已启动，同时支持HTTP和MQTT协议接收数据
2025-06-16 15:13:55,587 - INFO - 127.0.0.1 - "GET /api/current_visit HTTP/1.1" 200 -
2025-06-16 15:14:00,013 - INFO - 127.0.0.1 - "GET /api/current_visit HTTP/1.1" 200 -
2025-06-16 15:14:04,041 - ERROR - 心跳发送异常: HTTPConnectionPool(host='**************', port=80): Max retries exceeded with url: /1859128349437280256/SN20250605/heartbeat (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000298F7730160>, 'Connection to ************** timed out. (connect timeout=10.0)'))
2025-06-16 15:14:04,042 - ERROR - 请求URL: http://**************/1859128349437280256/SN20250605/heartbeat
2025-06-16 15:14:05,325 - INFO - 127.0.0.1 - "GET /api/current_visit HTTP/1.1" 200 -
