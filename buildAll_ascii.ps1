# buildAll_ascii.ps1
# Rehabilitation System HTTP/MQTT Dual Protocol Server Build Script v2.1.0
# ASCII version to avoid encoding issues

param(
    [switch]$SkipHttpServer,    # Skip HTTP server build
    [switch]$SkipElectron,      # Skip Electron build
    [switch]$OnlyConsole,       # Build console version only
    [switch]$OnlyNoConsole,     # Build no-console version only
    [switch]$TestCodeMapping,   # Test media code mapping
    [switch]$Help               # Show help
)

# Error handling
$ErrorActionPreference = "Stop"

# Color output functions
function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Write-Step {
    param([string]$Message)
    Write-Host "`n[STEP] $Message" -ForegroundColor Magenta
}

# Show help information
function Show-Help {
    Write-Host "Rehabilitation System HTTP/MQTT Dual Protocol Server Build Script v2.1.0" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Features:" -ForegroundColor Yellow
    Write-Host "   HTTP/MQTT dual protocol support"
    Write-Host "   Audio/Video code mapping (137 files)"
    Write-Host "   Console/No-console dual versions"
    Write-Host "   Smart logging system"
    Write-Host "   Complete frontend interface"
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "    .\buildAll_ascii.ps1 [parameters]"
    Write-Host ""
    Write-Host "Parameters:" -ForegroundColor Yellow
    Write-Host "    -SkipHttpServer     Skip HTTP/MQTT server build"
    Write-Host "    -SkipElectron       Skip Electron frontend build"
    Write-Host "    -OnlyConsole        Build console version only"
    Write-Host "    -OnlyNoConsole      Build no-console version only"
    Write-Host "    -TestCodeMapping    Test audio/video code mapping"
    Write-Host "    -Help               Show this help"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "    .\buildAll_ascii.ps1                      # Full build (recommended)"
    Write-Host "    .\buildAll_ascii.ps1 -OnlyConsole         # Console version only"
    Write-Host "    .\buildAll_ascii.ps1 -OnlyNoConsole       # No-console version only"
    Write-Host "    .\buildAll_ascii.ps1 -SkipElectron        # Server only"
    Write-Host "    .\buildAll_ascii.ps1 -TestCodeMapping     # Test code mapping"
}

# Check build environment
function Check-BuildEnvironment {
    Write-Step "Checking build environment"
    
    # Check Python
    try {
        $pythonVersion = python --version 2>&1
        Write-Info "Python version: $pythonVersion"
    }
    catch {
        Write-Error "Python not installed or not in PATH"
        return $false
    }
    
    # Check Node.js
    try {
        $nodeVersion = node --version 2>&1
        Write-Info "Node.js version: $nodeVersion"
    }
    catch {
        Write-Error "Node.js not installed or not in PATH"
        return $false
    }
    
    # Check required files
    $requiredFiles = @("http_server.py", "config.json", "package.json")
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            Write-Error "Required file not found: $file"
            return $false
        }
        Write-Info "OK $file exists"
    }
    
    return $true
}

# Build HTTP/MQTT dual protocol server
function Build-HttpServer {
    Write-Step "Building HTTP/MQTT dual protocol server"
    
    try {
        # Determine build mode
        $buildMode = "3"  # Default: build both versions
        if ($OnlyConsole) {
            $buildMode = "1"
            Write-Info "Build mode: Console version only"
        }
        elseif ($OnlyNoConsole) {
            $buildMode = "2"
            Write-Info "Build mode: No-console version only"
        }
        else {
            Write-Info "Build mode: Console + No-console dual versions"
        }
        
        # Execute build script
        Write-Info "Executing build script..."

        # Use echo to pipe input to Python script
        echo $buildMode | python build_server.py

        # Check if build was successful by looking for output files
        $serverFound = $false
        $buildSuccess = $true

        if (Test-Path "dist-server\RehabilitationServer.exe") {
            $fileSize = (Get-Item "dist-server\RehabilitationServer.exe").Length / 1MB
            $fileSizeStr = [math]::Round($fileSize, 2).ToString() + " MB"
            Write-Success "Console version: RehabilitationServer.exe ($fileSizeStr)"
            $serverFound = $true
        }
        if (Test-Path "dist-server\httpServer.exe") {
            $fileSize = (Get-Item "dist-server\httpServer.exe").Length / 1MB
            $fileSizeStr = [math]::Round($fileSize, 2).ToString() + " MB"
            Write-Success "No-console version (Production): httpServer.exe ($fileSizeStr)"
            $serverFound = $true
        }

        if ($serverFound) {
            Write-Success "HTTP/MQTT dual protocol server build successful"
        }
        else {
            Write-Error "No server files found"
            $buildSuccess = $false
        }

        if (-not $buildSuccess) {
            return $false
        }
    }
    catch {
        Write-Error "HTTP/MQTT server build exception: $($_.Exception.Message)"
        return $false
    }
    
    return $true
}

# Test code mapping functionality
function Test-CodeMapping {
    Write-Step "Testing audio/video code mapping functionality"
    
    try {
        if (-not (Test-Path "test_media_code_mapping.py")) {
            Write-Error "Test script not found: test_media_code_mapping.py"
            return $false
        }
        
        Write-Info "Executing code mapping test..."
        python test_media_code_mapping.py
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Code mapping test completed"
        }
        else {
            Write-Error "Code mapping test failed"
            return $false
        }
    }
    catch {
        Write-Error "Code mapping test exception: $($_.Exception.Message)"
        return $false
    }
    
    return $true
}

# Build Electron frontend installer
function Build-ElectronApp {
    Write-Step "Building Electron frontend installer"
    
    try {
        Write-Info "Executing: npm run build-win"
        npm run build-win
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Electron frontend installer build successful"
            
            # Check output directories
            $outputDirs = @("dist-installer", "dist", "build", "out")
            $found = $false
            
            foreach ($dir in $outputDirs) {
                if (Test-Path $dir) {
                    $installerFiles = Get-ChildItem $dir -Filter "*.exe" -ErrorAction SilentlyContinue
                    if ($installerFiles.Count -gt 0) {
                        foreach ($file in $installerFiles) {
                            $fileSize = $file.Length / 1MB
                            $fileSizeStr = [math]::Round($fileSize, 2).ToString() + " MB"
                            Write-Success "Frontend installer: $($file.Name) ($fileSizeStr) - Location: $dir"
                            $found = $true
                        }
                    }
                }
            }
            
            if (-not $found) {
                Write-Info "No .exe installer found"
            }
        }
        else {
            Write-Error "Electron frontend installer build failed"
            return $false
        }
    }
    catch {
        Write-Error "Electron frontend build exception: $($_.Exception.Message)"
        return $false
    }
    
    return $true
}

# Show build results
function Show-BuildResults {
    Write-Step "Build results summary"
    
    Write-Host "`n[RESULTS] Output files:" -ForegroundColor Yellow
    
    # HTTP/MQTT dual protocol server
    Write-Host "`nHTTP/MQTT Dual Protocol Server:" -ForegroundColor Cyan
    
    $serverFound = $false
    if (Test-Path "dist-server\RehabilitationServer.exe") {
        $size = (Get-Item "dist-server\RehabilitationServer.exe").Length / 1MB
        $sizeStr = [math]::Round($size, 2).ToString() + " MB"
        Write-Success "Console version: RehabilitationServer.exe ($sizeStr)"
        $serverFound = $true
    }
    if (Test-Path "dist-server\httpServer.exe") {
        $size = (Get-Item "dist-server\httpServer.exe").Length / 1MB
        $sizeStr = [math]::Round($size, 2).ToString() + " MB"
        Write-Success "No-console version (Production): httpServer.exe ($sizeStr)"
        $serverFound = $true
    }
    
    if (-not $serverFound) {
        Write-Error "No HTTP/MQTT server files found"
    }
    
    # Electron frontend installer
    Write-Host "`nElectron Frontend Installer:" -ForegroundColor Cyan
    $found = $false
    $searchDirs = @("dist-installer", "dist", "build", "out")
    
    foreach ($dir in $searchDirs) {
        if (Test-Path $dir) {
            $installerFiles = Get-ChildItem $dir -Filter "*.exe" -ErrorAction SilentlyContinue
            if ($installerFiles.Count -gt 0) {
                foreach ($file in $installerFiles) {
                    $size = $file.Length / 1MB
                    $sizeStr = [math]::Round($size, 2).ToString() + " MB"
                    Write-Success "Frontend installer: $($file.Name) ($sizeStr) - Location: $dir"
                    $found = $true
                }
            }
        }
    }
    
    if (-not $found) {
        Write-Info "No Electron frontend installer found"
    }
    
    # Feature summary
    Write-Host "`nFeatures:" -ForegroundColor Yellow
    Write-Host "   HTTP/MQTT dual protocol support"
    Write-Host "   Audio/Video code auto-mapping (137 files)"
    Write-Host "   Console/No-console dual versions"
    Write-Host "   Smart logging system"
    Write-Host "   Complete frontend interface"
    Write-Host "   Platform MQTT protocol v2.0 compliant"
}

# Main function
function Main {
    # Show title
    Write-Host "================================================================" -ForegroundColor Cyan
    Write-Host "    Rehabilitation System HTTP/MQTT Build Script v2.1.0" -ForegroundColor Cyan
    Write-Host "================================================================" -ForegroundColor Cyan
    
    # Show help
    if ($Help) {
        Show-Help
        return
    }
    
    # Special function: test code mapping
    if ($TestCodeMapping) {
        Test-CodeMapping
        return
    }
    
    # Record start time
    $startTime = Get-Date
    Write-Info "Build start time: $($startTime.ToString('yyyy-MM-dd HH:mm:ss'))"
    
    try {
        # Check build environment
        if (-not (Check-BuildEnvironment)) {
            Write-Error "Build environment check failed, stopping build"
            exit 1
        }
        
        # Build HTTP/MQTT dual protocol server
        if (-not $SkipHttpServer) {
            if (-not (Build-HttpServer)) {
                Write-Error "HTTP/MQTT server build failed, stopping build"
                exit 1
            }
        }
        else {
            Write-Info "Skipping HTTP/MQTT server build"
        }
        
        # Build Electron frontend installer
        if (-not $SkipElectron) {
            if (-not (Build-ElectronApp)) {
                Write-Error "Electron frontend installer build failed"
                # Don't exit, continue to show results
            }
        }
        else {
            Write-Info "Skipping Electron frontend installer build"
        }
        
        # Show results
        Show-BuildResults
        
        # Calculate duration
        $endTime = Get-Date
        $duration = $endTime - $startTime
        Write-Info "Total time: $($duration.ToString('mm\:ss'))"
        
        Write-Host "`n[COMPLETE] Build completed!" -ForegroundColor Green
        Write-Host "Output directories: dist-server/ (server), dist-installer/ (frontend)" -ForegroundColor Green
        
    }
    catch {
        Write-Error "Build process exception: $($_.Exception.Message)"
        exit 1
    }
}

# Execute main function
Main
