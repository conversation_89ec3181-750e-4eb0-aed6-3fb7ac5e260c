#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无界面应用打包脚本
创建不显示命令窗口的可执行文件
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        print("📦 正在安装PyInstaller...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PyInstaller安装成功")
            return True
        else:
            print(f"❌ PyInstaller安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装PyInstaller时发生异常: {e}")
        return False

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['http_server.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('dist-server/config.json', 'dist-server/'),
        ('config-mqtt-example.json', '.'),
    ],
    hiddenimports=[
        'paho.mqtt.client',
        'hashlib',
        'json',
        'time',
        'threading',
        'datetime',
        'http.server',
        'urllib.parse',
        'requests',
        'os',
        'logging'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='RehabilitationServer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 关键：设置为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('rehabilitation_server.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建spec文件: rehabilitation_server.spec")

def build_executable():
    """构建可执行文件"""
    try:
        print("🔨 开始构建无界面可执行文件...")
        
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "rehabilitation_server.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 可执行文件构建成功")
            print("📄 构建输出:")
            print(result.stdout)
            return True
        else:
            print("❌ 可执行文件构建失败")
            print("📄 错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生异常: {e}")
        return False

def create_distribution():
    """创建分发包"""
    try:
        dist_dir = f"RehabilitationServer_NoConsole_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if os.path.exists(dist_dir):
            shutil.rmtree(dist_dir)
        
        os.makedirs(dist_dir)
        
        # 复制可执行文件
        exe_source = "dist/RehabilitationServer.exe"
        if os.path.exists(exe_source):
            shutil.copy2(exe_source, dist_dir)
            print(f"✅ 已复制可执行文件到: {dist_dir}")
        else:
            print(f"❌ 找不到可执行文件: {exe_source}")
            return False
        
        # 复制配置文件
        config_files = [
            "config.json",
            "config-mqtt-example.json"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, dist_dir)
                print(f"✅ 已复制配置文件: {config_file}")
        
        # 创建启动脚本
        start_script = f"""@echo off
echo 康复系统服务器正在启动...
echo 这是一个无界面应用，服务器将在后台运行
echo 日志文件将保存在 logs 目录中
echo.
echo 要停止服务器，请关闭此窗口或按 Ctrl+C
echo.

RehabilitationServer.exe

echo.
echo 服务器已停止
pause
"""
        
        with open(os.path.join(dist_dir, "启动服务器.bat"), 'w', encoding='gbk') as f:
            f.write(start_script)
        
        # 创建说明文件
        readme_content = f"""# 康复系统HTTP/MQTT双协议服务器 (无界面版本)

## 文件说明
- RehabilitationServer.exe: 主程序文件（无界面版本）
- config.json: 配置文件
- config-mqtt-example.json: MQTT配置示例
- 启动服务器.bat: 快速启动脚本

## 启动方法
1. 双击"RehabilitationServer.exe"启动服务器（无界面）
2. 或者双击"启动服务器.bat"启动（有提示窗口）

## 特点
- 无界面运行，不显示命令窗口
- 所有日志输出到 logs 目录下的日志文件
- 支持HTTP和MQTT双协议
- 自动处理音视频编码映射

## 日志查看
服务器运行时，日志文件保存在：
logs/YYYYMMDD/log.txt

## 配置修改
编辑 config.json 文件可以修改：
- HTTP服务器端口（默认8888）
- MQTT服务器配置
- 平台接口地址
- 设备ID和Token

## 音视频编码支持
服务器内置了完整的音视频编码映射表，可以自动将平台下发的编码转换为对应的文件名。

## 注意事项
1. 确保端口8888未被占用
2. 如需MQTT功能，请配置正确的MQTT服务器信息
3. 服务器运行时会自动创建必要的目录和文件
4. 要停止服务器，请结束进程或重启计算机

构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
版本: v2.0.0 (无界面版本)
"""
        
        with open(os.path.join(dist_dir, "README.txt"), 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✅ 分发包创建完成: {dist_dir}")
        print(f"📁 包含文件:")
        for file in os.listdir(dist_dir):
            print(f"   - {file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建分发包时发生异常: {e}")
        return False

def main():
    """主函数"""
    print("🏗️ 康复系统无界面应用打包工具")
    print("=" * 50)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("❌ 无法安装PyInstaller，打包失败")
            return
    
    # 创建spec文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        print("❌ 构建失败")
        return
    
    # 创建分发包
    if create_distribution():
        print("\n🎉 无界面应用打包完成！")
        print("💡 提示:")
        print("   - 可执行文件不会显示命令窗口")
        print("   - 所有日志都会保存到文件中")
        print("   - 可以在任务管理器中查看进程状态")
        print("   - 要停止服务器，请结束对应进程")
    else:
        print("❌ 分发包创建失败")

if __name__ == '__main__':
    main()
