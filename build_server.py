#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建康复系统HTTP/MQTT双协议服务器为exe文件
支持有界面和无界面两种模式
使用PyInstaller打包
版本: v2.1.0
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        import PyInstaller
        print(f" PyInstaller 版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print(" PyInstaller 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print(" PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(" PyInstaller 安装失败")
            return False

def check_mqtt_dependencies():
    """检查MQTT依赖"""
    try:
        import paho.mqtt.client as mqtt
        print(" MQTT库 (paho-mqtt) 已安装")
        return True
    except ImportError:
        print(" MQTT库未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "paho-mqtt"])
            print(" MQTT库安装成功")
            return True
        except subprocess.CalledProcessError:
            print(" MQTT库安装失败")
            return False

def check_build_environment():
    """检查构建环境"""
    print("检查构建环境...")

    # 检查必要文件
    required_files = ["http_server.py", "config.json"]
    optional_files = ["httpServer.spec", "config-mqtt-example.json"]
    missing_files = []

    for file in required_files:
        if os.path.exists(file):
            print(f" {file} 存在")
        else:
            print(f" {file} 不存在")
            missing_files.append(file)

    for file in optional_files:
        if os.path.exists(file):
            print(f" {file} 存在")
        else:
            print(f" {file} 不存在 (可选)")

    if missing_files:
        print(f"错误: 缺少必要文件: {', '.join(missing_files)}")
        return False

    # 检查图标文件
    icon_file = "assets/icon.ico"
    if os.path.exists(icon_file):
        print(f" {icon_file} 存在")
    else:
        print(f" {icon_file} 不存在，将使用默认图标")

    # 检查Python版本
    python_version = sys.version_info
    print(f" Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")

    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print(" 警告: Python版本过低，建议使用Python 3.7+")

    return True

def create_spec_files():
    """创建PyInstaller spec文件"""
    print("创建PyInstaller spec文件...")

    # 有界面版本spec文件
    console_spec = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['http_server.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('config-mqtt-example.json', '.'),
    ],
    hiddenimports=[
        'paho.mqtt.client',
        'hashlib',
        'json',
        'time',
        'threading',
        'datetime',
        'http.server',
        'urllib.parse',
        'requests',
        'os',
        'logging'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='RehabilitationServer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 有界面版本
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''

    # 无界面版本spec文件
    no_console_spec = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['http_server.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('config-mqtt-example.json', '.'),
    ],
    hiddenimports=[
        'paho.mqtt.client',
        'hashlib',
        'json',
        'time',
        'threading',
        'datetime',
        'http.server',
        'urllib.parse',
        'requests',
        'os',
        'logging'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='httpServer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无界面版本
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''

    # 写入spec文件
    with open('httpServer.spec', 'w', encoding='utf-8') as f:
        f.write(console_spec)
    print("已创建 rehabilitation_server_no_console.spec (有界面版本)")

    with open('httpServer.spec', 'w', encoding='utf-8') as f:
        f.write(no_console_spec)
    print(" 已创建 httpServer.spec (无界面版本)")

def build_exe(build_mode='both'):
    """构建exe文件

    Args:
        build_mode: 构建模式 ('console', 'no_console', 'both')
    """
    print(" 开始构建康复系统服务器...")

    # 确保输出目录存在
    os.makedirs("dist-server", exist_ok=True)
    os.makedirs("build-server", exist_ok=True)

    build_results = []

    if build_mode in ['console', 'both']:
        print("\n 构建有界面版本...")
        result = build_single_exe("rehabilitation_server_no_console.spec", "RehabilitationServer.exe", "有界面版本")
        build_results.append(('有界面版本', result))

    if build_mode in ['no_console', 'both']:
        print("\n 构建无界面版本...")
        result = build_single_exe("httpServer.spec", "httpServer.exe", "无界面版本")
        build_results.append(('无界面版本', result))

    return build_results

def build_single_exe(spec_file, exe_name, description):
    """构建单个exe文件"""
    print(f"正在构建 {description}: {exe_name}")

    # 使用spec文件构建，确保输出到dist-server目录
    cmd = [
        "pyinstaller",
        "--distpath=dist-server",       # 指定输出目录
        "--workpath=build-server",      # 指定工作目录
        "--clean",                      # 清理缓存
        spec_file                       # 使用spec文件
    ]

    try:
        print(f"️ 执行PyInstaller构建 {description}...")

        # 使用更安全的subprocess调用方式
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,  # 合并stderr到stdout
            text=True,
            encoding='utf-8',
            errors='replace',  # 替换无法解码的字符
            bufsize=1,
            universal_newlines=True
        )

        # 实时输出构建过程
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                output_lines.append(line)
                # 只显示重要信息，避免输出过多
                if any(keyword in line for keyword in ['INFO:', 'ERROR:', 'WARNING:', '完成', '成功', '失败']):
                    print(f"  {line}")

        # 等待进程完成
        return_code = process.poll()

        if return_code == 0:
            print(f"{description} 构建成功！")

            # 检查输出文件
            output_file = f"dist-server/{exe_name}"
            if os.path.exists(output_file):
                # 获取文件大小
                file_size = os.path.getsize(output_file)
                print(f" exe文件大小: {file_size / 1024 / 1024:.2f} MB")
                print(f" exe文件位置: {os.path.abspath(output_file)}")

                return True
            else:
                print(f" 构建失败：未找到输出文件 {output_file}")
                return False
        else:
            print(f" {description} 构建失败，返回码: {return_code}")
            # 显示最后几行输出用于调试
            print("最后的输出信息:")
            for line in output_lines[-10:]:
                print(f"  {line}")
            return False

    except Exception as e:
        print(f" {description} 构建过程异常: {e}")
        return False

def create_startup_scripts():
    """创建启动脚本"""

    # 有界面版本启动脚本
    console_script = """@echo off
chcp 65001 >nul
title 康复系统HTTP/MQTT双协议服务器 (有界面版本)
echo ================================================
echo 康复系统HTTP/MQTT双协议服务器 (有界面版本)
echo ================================================
echo.
echo 正在启动服务器...
echo HTTP服务器地址: http://localhost:8888
echo MQTT协议: 支持平台标准MQTT协议v2.0
echo 编码映射: 支持137个音视频文件编码自动映射
echo.
echo 按 Ctrl+C 停止服务器
echo ================================================

httpServer.exe

echo.
echo 服务器已停止
pause
"""

    # 无界面版本启动脚本
    no_console_script = """@echo off
chcp 65001 >nul
echo 康复系统HTTP/MQTT双协议服务器 (无界面版本)
echo ================================================
echo.
echo 正在启动无界面服务器...
echo 注意：这是无界面模式，服务器将在后台运行
echo 所有日志将保存到 logs 目录中
echo.
echo HTTP服务器地址: http://localhost:8888
echo MQTT协议: 支持平台标准MQTT协议v2.0
echo 编码映射: 支持137个音视频文件编码自动映射
echo.
echo 要停止服务器，请关闭此窗口或在任务管理器中结束进程
echo ================================================

start "" "RehabilitationServer.exe"

echo.
echo 无界面服务器已在后台启动
echo 可以通过任务管理器查看 RehabilitationServer.exe 进程
echo 日志文件位置: logs\\当前日期\\log.txt
pause
"""

    # 测试脚本
    test_script = """@echo off
chcp 65001 >nul
echo 康复系统编码映射测试工具
echo ========================
echo.
echo 正在启动编码映射测试...
echo 请确保康复系统服务器已经运行
echo.

python test_media_code_mapping.py

echo.
echo 测试完成
pause
"""

    # 写入启动脚本
    with open("dist-server/启动服务器(有界面).bat", "w", encoding="utf-8") as f:
        f.write(console_script)
    print(" 已创建: dist-server/启动服务器(有界面).bat")

    with open("dist-server/启动服务器(无界面).bat", "w", encoding="utf-8") as f:
        f.write(no_console_script)
    print(" 已创建: dist-server/启动服务器(无界面).bat")

    with open("dist-server/测试编码映射.bat", "w", encoding="utf-8") as f:
        f.write(test_script)
    print(" 已创建: dist-server/测试编码映射.bat")

def create_readme():
    """创建说明文档"""
    readme_content = f"""# 康复系统HTTP/MQTT双协议服务器 v2.1.0

##  文件说明

### 主程序文件
- **httpServer.exe**: 有界面版本（显示控制台窗口）
- **RehabilitationServer.exe**: 无界面版本（后台运行）
- **config.json**: 主配置文件
- **config-mqtt-example.json**: MQTT配置示例

### 启动脚本
- **启动服务器(有界面).bat**: 启动有界面版本
- **启动服务器(无界面).bat**: 启动无界面版本
- **测试编码映射.bat**: 测试音视频编码映射功能

##  启动方法

### 方法1: 直接运行
- 有界面版本: 双击 `httpServer.exe`
- 无界面版本: 双击 `RehabilitationServer.exe`

### 方法2: 使用启动脚本
- 有界面版本: 双击 `启动服务器(有界面).bat`
- 无界面版本: 双击 `启动服务器(无界面).bat`

##  配置说明

编辑 `config.json` 文件可以修改：

### HTTP服务器配置
- `httpserver.host`: 服务器监听地址（默认0.0.0.0）
- `httpserver.port`: 服务器端口（默认8888）

### MQTT协议配置
- `mqtt.enabled`: 是否启用MQTT协议（true/false）
- `mqtt.broker_host`: MQTT服务器地址
- `mqtt.broker_port`: MQTT服务器端口（默认1883）
- `mqtt.auth.secure_id`: 平台分配的设备安全ID
- `mqtt.auth.secure_key`: 平台分配的设备安全密钥
- `mqtt.device.product_id`: 产品ID
- `mqtt.device.device_id`: 设备ID

### 平台接口配置
- `platform.server`: 平台服务器地址
- `platform.token`: 平台访问令牌
- `platform.device_id`: 设备标识
- `platform.timeout`: 请求超时时间

##  API接口

服务器启动后，可通过以下HTTP接口访问：

### 1. 接收康复信息
- **URL**: `POST http://localhost:8888/api/receive_rehabilitation_info`
- **用途**: 接收平台下发的康复信息
- **支持**: 自动处理音视频编码映射

### 2. 获取康复内容
- **URL**: `POST http://localhost:8888/api/get_rehabilitation_content`
- **用途**: 康复系统请求获取康复内容

### 3. 上报播放统计
- **URL**: `POST http://localhost:8888/api/report_play_statistics`
- **用途**: 上报播放时长统计

### 4. 服务器状态
- **URL**: `GET http://localhost:8888/api/status`
- **用途**: 查询服务器运行状态

### 5. 当前就诊信息
- **URL**: `GET http://localhost:8888/api/current_visit`
- **用途**: 查询当前就诊信息

##  MQTT协议支持

### 平台标准主题格式
- 属性读取: `/{productId}/{deviceId}/properties/read`
- 属性写入: `/{productId}/{deviceId}/properties/write`
- 功能调用: `/{productId}/{deviceId}/function/invoke`
- 属性上报: `/{productId}/{deviceId}/properties/report`
- 事件上报: `/{productId}/{deviceId}/event/{eventId}`

### 认证方式
- **username**: `secureId + "|" + timestamp`
- **password**: `md5(secureId + "|" + timestamp + "|" + secureKey)`

##  音视频编码映射

系统内置137个音视频文件的编码映射表，支持：

### 编码示例
- `72539895` → `1.止损负面情绪.mp3`
- `45577303` → `1.你是爱和光.mp3`
- `33326899` → `5分钟缓解焦虑.mp4`

### 支持格式
- 单个编码: `"media_code": "72539895"`
- 编码列表: `"media_codes": "72539895,45577303,33326899"`
- 媒体列表: `"media_list": [{{"code": "72539895"}}]`

##  日志文件

程序运行时会在logs目录下按日期创建日志文件：
- `logs/{datetime.now().strftime('%Y-%m-%d')}/log.txt` - 当日日志
- 自动按日期分类，便于日志管理和查看
- 无界面版本只输出到日志文件

## 🔧 技术特性

### 双协议支持
-  HTTP协议：兼容现有前端系统
-  MQTT协议：符合平台标准v2.0规范
-  统一数据处理：两种协议接收的数据统一处理

### 编码映射
-  自动编码转换：平台编码自动映射为文件名
-  多格式支持：支持多种编码数据格式
-  前端透明：前端无需关心编码处理

### 运行模式
-  有界面模式：显示控制台，便于调试
-  无界面模式：后台运行，适合生产环境
-  智能日志：根据运行环境自动调整日志输出

##  测试工具

### 编码映射测试
运行 `测试编码映射.bat` 可以测试：
- 编码到文件名的映射
- HTTP接口的编码处理
- 数据处理流程验证

##  技术支持

如有问题请联系技术支持团队。

---
**版本**: v2.1.0
**更新日期**: {datetime.now().strftime('%Y-%m-%d')}
**新增功能**: MQTT协议支持 + 音视频编码映射 + 无界面模式
**兼容性**: 完全向后兼容
"""

    with open("dist-server/README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)

    print(" 已创建: dist-server/README.txt")

def test_exe_independence():
    """测试exe文件的独立性"""
    print("\n 测试exe文件独立性...")

    exe_files = []
    if os.path.exists("dist-server/httpServer.exe"):
        exe_files.append(("httpServer.exe", "有界面版本"))
    if os.path.exists("dist-server/RehabilitationServer.exe"):
        exe_files.append(("RehabilitationServer.exe", "无界面版本"))

    if not exe_files:
        print(" 没有找到任何exe文件")
        return False

    for exe_name, description in exe_files:
        print(f"\n 测试 {description} ({exe_name}):")

        output_file = f"dist-server/{exe_name}"

        # 检查文件大小（独立exe通常较大）
        file_size = os.path.getsize(output_file)
        print(f"    文件大小: {file_size / 1024 / 1024:.2f} MB")

        if file_size < 10 * 1024 * 1024:  # 小于10MB可能不是完全独立的
            print("    警告: 文件大小较小，可能不是完全独立的exe")
        else:
            print("    文件大小正常，应该是完全独立的exe")

    # 检查是否包含必要的配置文件
    config_files = [
        ("config.json", "主配置文件"),
        ("config-mqtt-example.json", "MQTT配置示例"),
        ("README.txt", "说明文档")
    ]

    print(f"\n 检查配置文件:")
    for config_file, description in config_files:
        full_path = f"dist-server/{config_file}"
        if os.path.exists(full_path):
            print(f"    {description}: {config_file}")
        else:
            print(f"    {description}: {config_file} (不存在)")

    print(" 独立性测试完成")
    return True

def clean_build_cache():
    """清理构建缓存"""
    print("清理构建缓存...")

    cache_dirs = ["build-server", "__pycache__"]
    cache_files = ["*.pyc", "*.pyo"]

    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"[OK] 已清理: {cache_dir}")
            except Exception as e:
                print(f"[WARN] 清理失败 {cache_dir}: {e}")

    # 清理pyc文件
    import glob
    for pattern in cache_files:
        for file in glob.glob(pattern):
            try:
                os.remove(file)
                print(f"[OK] 已清理: {file}")
            except Exception as e:
                print(f"[WARN] 清理失败 {file}: {e}")

    print("缓存清理完成")

def copy_additional_files():
    """复制额外的文件到分发目录"""
    print(" 复制额外文件...")

    files_to_copy = [
        ("config.json", "dist-server/"),
        ("config-mqtt-example.json", "dist-server/"),
    ]

    # 如果测试文件存在，也复制过去
    if os.path.exists("test_media_code_mapping.py"):
        files_to_copy.append(("test_media_code_mapping.py", "dist-server/"))

    for src, dst_dir in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, dst_dir)
            print(f" 已复制: {src} -> {dst_dir}")
        else:
            print(f" 文件不存在: {src}")

def main():
    """主函数"""
    print("康复系统HTTP/MQTT双协议服务器构建工具 v2.1.0")
    print("=" * 60)

    # 询问构建模式
    print("请选择构建模式:")
    print("1. 有界面版本 (显示控制台窗口)")
    print("2. 无界面版本 (后台运行)")
    print("3. 两个版本都构建 (推荐)")

    while True:
        choice = input("\n请输入选择 (1/2/3): ").strip()
        if choice == '1':
            build_mode = 'console'
            break
        elif choice == '2':
            build_mode = 'no_console'
            break
        elif choice == '3':
            build_mode = 'both'
            break
        else:
            print(" 无效选择，请输入 1、2 或 3")

    print(f"\n 构建模式: {build_mode}")
    print("=" * 60)

    # 检查构建环境
    if not check_build_environment():
        return False

    # 检查并安装PyInstaller
    if not check_pyinstaller():
        return False

    # 检查MQTT依赖
    if not check_mqtt_dependencies():
        return False

    # 清理构建缓存
    clean_build_cache()

    print("\n" + "-" * 60)

    # 创建spec文件
    create_spec_files()

    print("\n" + "-" * 60)

    # 构建exe
    build_results = build_exe(build_mode)

    # 检查构建结果
    success_count = sum(1 for _, result in build_results if result)
    total_count = len(build_results)

    if success_count == 0:
        print("所有构建都失败了")
        return False

    print("\n" + "-" * 60)

    # 复制额外文件
    copy_additional_files()

    # 创建启动脚本和说明文档
    create_startup_scripts()
    create_readme()

    # 测试exe独立性
    test_exe_independence()

    print("\n" + "=" * 60)
    print(" 构建完成！")
    print(f" 构建结果: {success_count}/{total_count} 成功")

    for version, result in build_results:
        status = " 成功" if result else " 失败"
        print(f"   {version}: {status}")

    print("\n 输出目录: dist-server/")
    print(" 主要文件:")

    if build_mode in ['console', 'both']:
        print("   - httpServer.exe (有界面版本)")
        print("   - 启动服务器(有界面).bat")

    if build_mode in ['no_console', 'both']:
        print("   - RehabilitationServer.exe (无界面版本)")
        print("   - 启动服务器(无界面).bat")

    print("   - config.json (配置文件)")
    print("   - config-mqtt-example.json (MQTT配置示例)")
    print("   - README.txt (详细说明)")
    print("   - 测试编码映射.bat (测试工具)")

    print("\n 特性:")
    print("    HTTP/MQTT双协议支持")
    print("    音视频编码自动映射")
    print("    智能日志系统")
    print("    完全独立运行，不依赖Python环境")
    print("    符合平台MQTT协议v2.0标准")

    print("=" * 60)

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        # 检测是否在自动化环境中运行
        try:
            import sys
            if hasattr(sys.stdin, 'isatty') and sys.stdin.isatty():
                input("按回车键退出...")
        except:
            pass
        sys.exit(1)
