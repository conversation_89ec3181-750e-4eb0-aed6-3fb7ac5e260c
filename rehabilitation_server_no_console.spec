# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['http_server.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('config-mqtt-example.json', '.'),
    ],
    hiddenimports=[
        'paho.mqtt.client',
        'hashlib',
        'json',
        'time',
        'threading',
        'datetime',
        'http.server',
        'urllib.parse',
        'requests',
        'os',
        'logging'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='RehabilitationServer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无界面版本
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
