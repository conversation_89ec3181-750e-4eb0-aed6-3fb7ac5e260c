{"server": {"host": "0.0.0.0", "port": 8080}, "platform": {"api_base_url": "http://*************", "endpoints": {"report_statistics": "/api/report_play_statistics", "report_completion": "/api/report_task_completion", "get_patient_info": "/api/get_patient_info", "health_check": "/api/health"}, "product_id": "rehabilitation_system", "device_id": "device_001", "token": "1859128349437280256", "timeout": 10000, "retry_attempts": 3, "retry_delay": 1000}, "httpserver": {"host": "0.0.0.0", "port": 8888, "endpoints": {"receive_rehabilitation_info": "/api/receive_rehabilitation_info", "current_visit": "/api/current_visit", "report_play_statistics": "/api/report_play_statistics"}}, "mqtt": {"enabled": true, "broker_host": "*************", "broker_port": 1883, "username": "", "password": "", "client_id": "rehabilitation_device_001", "topics": {"receive_rehabilitation_info": "rehabilitation/device_001/receive_info", "report_statistics": "rehabilitation/device_001/report_stats", "heartbeat": "rehabilitation/device_001/heartbeat"}, "qos": 1, "keep_alive": 60, "clean_session": true, "reconnect_delay": 5}, "heartbeat_interval": 60, "log_level": "INFO"}