{"server": {"host": "0.0.0.0", "port": 8080}, "platform": {"api_base_url": "http://**************", "endpoints": {"report_statistics": "/api/report_play_statistics", "report_completion": "/api/report_task_completion", "get_patient_info": "/api/get_patient_info", "health_check": "/api/health"}, "product_id": "1859128349437280256", "device_id": "SN20250605", "token": "1859128349437280256", "timeout": 10000, "retry_attempts": 3, "retry_delay": 1000}, "httpserver": {"host": "0.0.0.0", "port": 8888, "endpoints": {"receive_rehabilitation_info": "/api/receive_rehabilitation_info", "current_visit": "/api/current_visit", "report_play_statistics": "/api/report_play_statistics"}}, "mqtt": {"enabled": true, "broker_host": "**************", "broker_port": 1883, "auth": {"secure_id": "1859128349437280256", "secure_key": "1859128349437280256", "client_id": "SN20250605"}, "device": {"product_id": "1859128349437280256", "device_id": "SN20250605"}, "topics": {"properties_read": "/{productId}/{deviceId}/properties/read", "properties_write": "/{productId}/{deviceId}/properties/write", "function_invoke": "/{productId}/{deviceId}/function/invoke", "properties_report": "/{productId}/{deviceId}/properties/report", "properties_report_reply": "/{productId}/{deviceId}/properties/report/reply", "event_report": "/{productId}/{deviceId}/event/{eventId}", "event_report_reply": "/{productId}/{deviceId}/event/{eventId}/reply"}, "qos": 1, "keep_alive": 60, "clean_session": true, "reconnect_delay": 5}, "heartbeat_interval": 60, "log_level": "INFO"}