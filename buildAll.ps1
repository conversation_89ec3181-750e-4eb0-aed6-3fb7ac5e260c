# buildAll.ps1
# 康复系统HTTP/MQTT双协议服务器完整构建脚本 v2.1.0
# 功能：构建HTTP/MQTT双协议服务器 + 构建Electron前端安装包
# 新增：支持有界面/无界面双版本 + MQTT协议 + 音视频编码映射

param(
    [switch]$SkipHttpServer,    # 跳过HTTP服务器构建
    [switch]$SkipElectron,      # 跳过Electron构建
    [switch]$OnlyConsole,       # 只构建有界面版本
    [switch]$OnlyNoConsole,     # 只构建无界面版本
    [switch]$TestCodeMapping,   # 测试编码映射功能
    [switch]$Help               # 显示帮助
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 彩色输出函数
function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Write-Step {
    param([string]$Message)
    Write-Host "`n[STEP] $Message" -ForegroundColor Magenta
}

# 显示帮助信息
function Show-Help {
    Write-Host "康复系统HTTP/MQTT双协议服务器完整构建脚本 v2.1.0" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "功能特性:" -ForegroundColor Yellow
    Write-Host "   HTTP/MQTT双协议支持"
    Write-Host "   音视频编码自动映射 (137个文件)"
    Write-Host "   有界面/无界面双版本"
    Write-Host "   智能日志系统"
    Write-Host "   完整的前端界面"
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "    .\buildAll.ps1 [参数]"
    Write-Host ""
    Write-Host "参数:" -ForegroundColor Yellow
    Write-Host "    -SkipHttpServer     跳过HTTP/MQTT服务器构建"
    Write-Host "    -SkipElectron       跳过Electron前端构建"
    Write-Host "    -OnlyConsole        只构建有界面版本服务器"
    Write-Host "    -OnlyNoConsole      只构建无界面版本服务器"
    Write-Host "    -TestCodeMapping    测试音视频编码映射功能"
    Write-Host "    -Help               显示此帮助信息"
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "    .\buildAll.ps1                      # 完整构建 (推荐)"
    Write-Host "    .\buildAll.ps1 -OnlyConsole         # 只构建有界面版本"
    Write-Host "    .\buildAll.ps1 -OnlyNoConsole       # 只构建无界面版本"
    Write-Host "    .\buildAll.ps1 -SkipElectron        # 只构建服务器"
    Write-Host "    .\buildAll.ps1 -TestCodeMapping     # 测试编码映射"
    Write-Host ""
    Write-Host "输出文件:" -ForegroundColor Yellow
    Write-Host "   服务器: dist-server/httpServer.exe (有界面)"
    Write-Host "          dist-server/RehabilitationServer.exe (无界面)"
    Write-Host "   前端:   dist-installer/*.exe (安装包)"
}

# 检查构建环境
function Check-BuildEnvironment {
    Write-Step "检查构建环境"

    # 检查Python
    try {
        $pythonVersion = python --version 2>&1
        Write-Info "Python版本: $pythonVersion"
    }
    catch {
        Write-Error "Python未安装或不在PATH中"
        return $false
    }

    # 检查Node.js
    try {
        $nodeVersion = node --version 2>&1
        Write-Info "Node.js版本: $nodeVersion"
    }
    catch {
        Write-Error "Node.js未安装或不在PATH中"
        return $false
    }

    # 检查必要文件
    $requiredFiles = @("http_server.py", "config.json", "package.json")
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            Write-Error "必要文件不存在: $file"
            return $false
        }
        Write-Info "OK $file 存在"
    }

    return $true
}

# 构建HTTP/MQTT双协议服务器
function Build-HttpServer {
    Write-Step "构建HTTP/MQTT双协议服务器"

    try {
        # 确定构建模式
        $buildMode = "3"  # 默认构建两个版本
        if ($OnlyConsole) {
            $buildMode = "1"
            Write-Info "构建模式: 仅有界面版本"
        }
        elseif ($OnlyNoConsole) {
            $buildMode = "2"
            Write-Info "构建模式: 仅无界面版本"
        }
        else {
            Write-Info "构建模式: 有界面 + 无界面双版本"
        }

        # 执行构建脚本
        Write-Info "执行: echo $buildMode | python build_server.py"
        $buildMode | python build_server.py

        if ($LASTEXITCODE -eq 0) {
            Write-Success "HTTP/MQTT双协议服务器构建成功"

            # 检查输出文件
            $serverFiles = @()
            if (Test-Path "dist-server\httpServer.exe") {
                $serverFiles += @{Name="httpServer.exe"; Path="dist-server\httpServer.exe"; Type="有界面版本"}
            }
            if (Test-Path "dist-server\RehabilitationServer.exe") {
                $serverFiles += @{Name="RehabilitationServer.exe"; Path="dist-server\RehabilitationServer.exe"; Type="无界面版本"}
            }

            if ($serverFiles.Count -eq 0) {
                Write-Error "未找到构建的服务器文件"
                return $false
            }

            foreach ($file in $serverFiles) {
                $fileSize = (Get-Item $file.Path).Length / 1MB
                $fileSizeStr = [math]::Round($fileSize, 2).ToString() + " MB"
                Write-Success "$($file.Type): $($file.Name) ($fileSizeStr)"
            }

            # 检查配置文件
            $configFiles = @("config.json", "config-mqtt-example.json", "README.txt")
            foreach ($configFile in $configFiles) {
                $fullPath = "dist-server\$configFile"
                if (Test-Path $fullPath) {
                    Write-Info "OK 配置文件: $configFile"
                }
            }
        }
        else {
            Write-Error "HTTP/MQTT服务器构建失败"
            return $false
        }
    }
    catch {
        Write-Error "HTTP/MQTT服务器构建异常: $($_.Exception.Message)"
        return $false
    }

    return $true
}

# 测试编码映射功能
function Test-CodeMapping {
    Write-Step "测试音视频编码映射功能"

    try {
        # 检查测试脚本是否存在
        if (-not (Test-Path "test_media_code_mapping.py")) {
            Write-Error "测试脚本不存在: test_media_code_mapping.py"
            return $false
        }

        Write-Info "执行编码映射测试..."
        python test_media_code_mapping.py

        if ($LASTEXITCODE -eq 0) {
            Write-Success "编码映射测试完成"
        }
        else {
            Write-Error "编码映射测试失败"
            return $false
        }
    }
    catch {
        Write-Error "编码映射测试异常: $($_.Exception.Message)"
        return $false
    }

    return $true
}

# 构建Electron前端安装包
function Build-ElectronApp {
    Write-Step "构建Electron前端安装包"

    try {
        # 检查package.json中的脚本
        if (Test-Path "package.json") {
            $packageJson = Get-Content "package.json" | ConvertFrom-Json
            if ($packageJson.scripts."build-win") {
                Write-Info "执行: npm run build-win"
                npm run build-win
            }
            elseif ($packageJson.scripts."build") {
                Write-Info "执行: npm run build"
                npm run build
            }
            else {
                Write-Info "执行: npm run dist"
                npm run dist
            }
        }
        else {
            Write-Error "package.json文件不存在"
            return $false
        }

        if ($LASTEXITCODE -eq 0) {
            Write-Success "Electron前端安装包构建成功"

            # 检查输出目录
            $outputDirs = @("dist-installer", "dist", "build", "out")
            $found = $false

            foreach ($dir in $outputDirs) {
                if (Test-Path $dir) {
                    $installerFiles = Get-ChildItem $dir -Filter "*.exe" -ErrorAction SilentlyContinue
                    if ($installerFiles.Count -gt 0) {
                        foreach ($file in $installerFiles) {
                            $fileSize = $file.Length / 1MB
                            $fileSizeStr = [math]::Round($fileSize, 2).ToString() + " MB"
                            Write-Success "前端安装包: $($file.Name) ($fileSizeStr) - 位置: $dir"
                            $found = $true
                        }
                    }
                }
            }

            if (-not $found) {
                Write-Info "未找到.exe安装包，检查其他输出文件..."
                foreach ($dir in $outputDirs) {
                    if (Test-Path $dir) {
                        $allFiles = Get-ChildItem $dir -ErrorAction SilentlyContinue
                        if ($allFiles.Count -gt 0) {
                            Write-Info "输出目录 $dir 包含 $($allFiles.Count) 个文件"
                        }
                    }
                }
            }
        }
        else {
            Write-Error "Electron前端安装包构建失败"
            return $false
        }
    }
    catch {
        Write-Error "Electron前端构建异常: $($_.Exception.Message)"
        return $false
    }

    return $true
}

# 显示构建结果
function Show-BuildResults {
    Write-Step "构建结果汇总"

    Write-Host "`n📊 [RESULTS] 输出文件:" -ForegroundColor Yellow

    # HTTP/MQTT双协议服务器
    Write-Host "`n🖥️ HTTP/MQTT双协议服务器:" -ForegroundColor Cyan

    $serverFiles = @(
        @{Name="httpServer.exe"; Path="dist-server\httpServer.exe"; Type="有界面版本"},
        @{Name="RehabilitationServer.exe"; Path="dist-server\RehabilitationServer.exe"; Type="无界面版本"}
    )

    $serverFound = $false
    foreach ($server in $serverFiles) {
        if (Test-Path $server.Path) {
            $size = (Get-Item $server.Path).Length / 1MB
            $sizeStr = [math]::Round($size, 2).ToString() + " MB"
            Write-Success "$($server.Type): $($server.Name) ($sizeStr)"
            $serverFound = $true
        }
    }

    if (-not $serverFound) {
        Write-Error "未找到HTTP/MQTT服务器文件"
    }

    # 配置文件和工具
    Write-Host "`n⚙️ 配置文件和工具:" -ForegroundColor Cyan
    $configFiles = @(
        @{Name="config.json"; Path="dist-server\config.json"; Desc="主配置文件"},
        @{Name="config-mqtt-example.json"; Path="dist-server\config-mqtt-example.json"; Desc="MQTT配置示例"},
        @{Name="README.txt"; Path="dist-server\README.txt"; Desc="详细说明文档"},
        @{Name="启动服务器(有界面).bat"; Path="dist-server\启动服务器(有界面).bat"; Desc="有界面启动脚本"},
        @{Name="启动服务器(无界面).bat"; Path="dist-server\启动服务器(无界面).bat"; Desc="无界面启动脚本"},
        @{Name="测试编码映射.bat"; Path="dist-server\测试编码映射.bat"; Desc="编码映射测试"}
    )

    foreach ($config in $configFiles) {
        if (Test-Path $config.Path) {
            Write-Success "$($config.Desc): $($config.Name)"
        }
        else {
            Write-Info "⚠️ $($config.Desc): $($config.Name) (未找到)"
        }
    }

    # Electron前端安装包
    Write-Host "`n🎨 Electron前端安装包:" -ForegroundColor Cyan
    $found = $false
    $searchDirs = @("dist-installer", "dist", "build", "out")

    foreach ($dir in $searchDirs) {
        if (Test-Path $dir) {
            $installerFiles = Get-ChildItem $dir -Filter "*.exe" -ErrorAction SilentlyContinue
            if ($installerFiles.Count -gt 0) {
                foreach ($file in $installerFiles) {
                    $size = $file.Length / 1MB
                    $sizeStr = [math]::Round($size, 2).ToString() + " MB"
                    Write-Success "前端安装包: $($file.Name) ($sizeStr) - 位置: $dir"
                    $found = $true
                }
            }
        }
    }

    if (-not $found) {
        Write-Info "⚠️ 未找到Electron前端安装包"
    }

    # 功能特性总结
    Write-Host "`n🚀 功能特性:" -ForegroundColor Yellow
    Write-Host "   ✅ HTTP/MQTT双协议支持"
    Write-Host "   ✅ 音视频编码自动映射 (137个文件)"
    Write-Host "   ✅ 有界面/无界面双版本服务器"
    Write-Host "   ✅ 智能日志系统"
    Write-Host "   ✅ 完整的前端界面"
    Write-Host "   ✅ 符合平台MQTT协议v2.0标准"
}

# 主函数
function Main {
    # 显示标题
    Write-Host "================================================================" -ForegroundColor Cyan
    Write-Host "    康复系统HTTP/MQTT双协议服务器完整构建脚本 v2.1.0" -ForegroundColor Cyan
    Write-Host "================================================================" -ForegroundColor Cyan

    # 显示帮助
    if ($Help) {
        Show-Help
        return
    }

    # 特殊功能：测试编码映射
    if ($TestCodeMapping) {
        Test-CodeMapping
        return
    }

    # 记录开始时间
    $startTime = Get-Date
    Write-Info "构建开始时间: $($startTime.ToString('yyyy-MM-dd HH:mm:ss'))"

    try {
        # 检查构建环境
        if (-not (Check-BuildEnvironment)) {
            Write-Error "构建环境检查失败，停止构建"
            exit 1
        }

        # 构建HTTP/MQTT双协议服务器
        if (-not $SkipHttpServer) {
            if (-not (Build-HttpServer)) {
                Write-Error "HTTP/MQTT服务器构建失败，停止构建"
                exit 1
            }
        }
        else {
            Write-Info "跳过HTTP/MQTT服务器构建"
        }

        # 构建Electron前端安装包
        if (-not $SkipElectron) {
            if (-not (Build-ElectronApp)) {
                Write-Error "Electron前端安装包构建失败"
                # 不退出，继续显示结果
            }
        }
        else {
            Write-Info "跳过Electron前端安装包构建"
        }

        # 显示结果
        Show-BuildResults

        # 计算耗时
        $endTime = Get-Date
        $duration = $endTime - $startTime
        Write-Info "总耗时: $($duration.ToString('mm\:ss'))"

        Write-Host "`n🎉 [COMPLETE] 构建完成！" -ForegroundColor Green
        Write-Host "📁 输出目录: dist-server/ (服务器), dist-installer/ (前端)" -ForegroundColor Green

    }
    catch {
        Write-Error "构建过程发生异常: $($_.Exception.Message)"
        exit 1
    }
}

# 执行主函数
Main
