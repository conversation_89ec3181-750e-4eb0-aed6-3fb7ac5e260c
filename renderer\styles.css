/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: relative;
}

/* 全屏模式下的窗口控制按钮 */
.fullscreen-controls {
    position: fixed;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 8px;
    z-index: 10000;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.fullscreen-controls:hover {
    opacity: 1;
}

.window-control-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    color: white;
}

.minimize-btn {
    background: rgba(255, 193, 7, 0.8);
}

.minimize-btn:hover {
    background: rgba(255, 193, 7, 1);
    transform: scale(1.1);
}

.close-btn {
    background: rgba(220, 53, 69, 0.8);
}

.close-btn:hover {
    background: rgba(220, 53, 69, 1);
    transform: scale(1.1);
}

/* 就诊信息栏样式 */
.patient-info-bar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    padding: 10px 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.patient-info-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    gap: 20px;
}

.patient-basic-info {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0;
}

.patient-media-info {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    min-width: 0;
}

.info-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 12px;
}

.info-value {
    font-weight: 500;
    background: transparent;
    border: none;
    padding: 0;
    min-width: 60px;
    text-align: left;
    font-size: 12px;
    color: #495057;
}

.info-separator {
    display: none;
}

.media-scroll-container {
    flex: 1;
    max-width: 300px;
    overflow: hidden;
    background: transparent;
    border: none;
    padding: 0;
    margin: 0 8px;
}

.media-names-scroll {
    white-space: nowrap;
    animation: scrollText 20s linear infinite;
    display: inline-block;
    min-width: 100%;
    font-size: 12px;
    color: #495057;
}

.media-names-scroll.no-scroll {
    animation: none;
}

@keyframes scrollText {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.toolbar-left, .toolbar-right {
    display: flex;
    gap: 10px;
}

.toolbar-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
}

.app-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

.btn-icon {
    padding: 8px;
    background: transparent;
    color: #666;
}

.btn-icon:hover {
    background: rgba(0, 0, 0, 0.1);
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-tab {
    background: rgba(255, 255, 255, 0.3);
    color: #666;
    border-radius: 20px;
}

.btn-tab.active {
    background: #007bff;
    color: white;
}

.btn-tab:hover {
    background: rgba(0, 123, 255, 0.7);
    color: white;
}

/* 主内容区域 */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    gap: 0;
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

/* 右侧栏样式 */
.right-sidebar {
    width: 280px;
    min-width: 280px;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-left: 2px solid rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
    z-index: 1;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

/* 目录树样式 */
.directory-tree {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
}

.tree-section {
    margin-bottom: 5px;
}

.tree-node {
    padding: 8px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.2s ease;
    user-select: none;
}

.tree-node:hover {
    background: rgba(0, 0, 0, 0.05);
}

.tree-node.active {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
    font-weight: 500;
}

.root-node {
    font-weight: 600;
    background: rgba(0, 0, 0, 0.02);
}

.child-node {
    padding-left: 45px;
    font-size: 14px;
}

.node-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.node-label {
    flex: 1;
}

.node-count {
    font-size: 12px;
    color: #666;
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 6px;
    border-radius: 8px;
    min-width: 20px;
    text-align: center;
}

.tree-children {
    display: block;
}

.tree-children.collapsed {
    display: none;
}

.playlist {
    list-style: none;
}

.playlist-item {
    padding: 12px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background 0.2s ease;
}

.playlist-item:hover {
    background: rgba(0, 0, 0, 0.05);
}

.playlist-item.active {
    background: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

.track-name {
    font-weight: 500;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.track-path {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.empty-hint {
    font-size: 12px;
    margin-top: 8px;
    line-height: 1.4;
}

/* 媒体库样式 */
.media-library {
    height: 100%;
}

.category-section {
    margin-bottom: 10px;
}

.category-header {
    padding: 8px 20px;
    background: rgba(0, 0, 0, 0.05);
    border-left: 3px solid #007bff;
    font-weight: 600;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.2s ease;
}

.category-header:hover {
    background: rgba(0, 0, 0, 0.1);
}

.category-header.collapsed {
    border-left-color: #ccc;
}

.category-toggle {
    font-size: 12px;
    color: #666;
    transition: transform 0.2s ease;
}

.category-header.collapsed .category-toggle {
    transform: rotate(-90deg);
}

.category-files {
    max-height: 300px;
    overflow-y: auto;
    transition: max-height 0.3s ease;
}

.category-files.collapsed {
    max-height: 0;
    overflow: hidden;
}

.media-item {
    padding: 10px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background 0.2s ease;
    padding-left: 35px;
}

.media-item:hover {
    background: rgba(0, 0, 0, 0.05);
}

.media-item.active {
    background: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

.media-name {
    font-weight: 500;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
}

.media-info {
    font-size: 11px;
    color: #666;
    display: flex;
    justify-content: space-between;
}

.media-size {
    color: #999;
}

/* 内容区域 */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

/* 文件列表区域 */
.file-list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.list-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.5);
}

.breadcrumb {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.list-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-count {
    font-size: 12px;
    color: #666;
}

.file-list-wrapper {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.file-list {
    padding: 10px 0;
}

.file-item {
    padding: 12px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: background 0.2s ease;
    cursor: pointer;
}

.file-item:hover {
    background: rgba(0, 0, 0, 0.03);
}

.file-item.playing {
    background: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

.file-icon {
    font-size: 20px;
    width: 30px;
    text-align: center;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-details {
    font-size: 12px;
    color: #666;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.file-category {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    white-space: nowrap;
}

.file-size {
    color: #999;
}

.file-actions {
    display: flex;
    gap: 5px;
}

.play-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: #007bff;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 14px;
}

.play-btn:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.play-btn.playing {
    background: #dc3545;
}

.no-selection {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-selection-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

/* 分页样式 */
.pagination {
    padding: 15px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.5);
}

.page-info {
    font-size: 14px;
    color: #666;
    min-width: 120px;
    text-align: center;
}

/* 播放器容器 */
.player-container {
    min-height: 200px;
    max-height: 300px;
    height: auto;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.media-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.video-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 800px;
    height: 500px;
    min-width: 400px;
    min-height: 300px;
    max-width: 90vw;
    max-height: 90vh;
    background: rgba(0, 0, 0, 0.95);
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
    z-index: 1000;
    resize: both;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.1);
    cursor: move;
}

/* 视频窗口标题栏 */
.video-titlebar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px 12px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    cursor: move;
    user-select: none;
    z-index: 1002;
}

.video-title {
    color: white;
    font-size: 14px;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    margin-right: 10px;
}

.video-container video {
    width: 100%;
    height: calc(100% - 40px);
    margin-top: 40px;
    border-radius: 0 0 12px 12px;
    object-fit: contain;
    background: black;
}

/* 视频关闭按钮 */
.video-close-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.video-close-btn:hover {
    background: rgba(220, 53, 69, 0.8);
    transform: scale(1.1);
}

.video-close-btn .icon {
    font-size: 14px;
    font-weight: bold;
}

.audio-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.audio-visualizer {
    text-align: center;
    max-width: 400px;
    padding: 20px;
}

.album-art {
    width: 150px;
    height: 150px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.default-album {
    font-size: 48px;
    color: white;
}

.track-info h2 {
    font-size: 20px;
    margin-bottom: 6px;
    color: #333;
}

.track-info p {
    font-size: 14px;
    color: #666;
}

/* 控制栏样式 */
.controls {
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.control-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.control-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
}

.play-btn {
    width: 60px;
    height: 60px;
    background: #007bff;
    color: white;
}

.play-btn:hover {
    background: #0056b3;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
}

.time {
    font-size: 14px;
    color: #666;
    min-width: 45px;
    text-align: center;
}

.volume-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.volume-bar {
    width: 100px;
}

/* 滑块样式 */
.slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.1);
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
}

/* 状态栏样式 */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 20px;
    background: rgba(0, 0, 0, 0.05);
    font-size: 12px;
    color: #666;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 右键菜单样式 */
.context-menu {
    position: fixed;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    min-width: 150px;
}

.context-menu ul {
    list-style: none;
    margin: 0;
    padding: 5px 0;
}

.context-menu li {
    padding: 8px 15px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.context-menu li:hover {
    background: rgba(0, 0, 0, 0.05);
}

.context-menu .separator {
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    margin: 5px 0;
    cursor: default;
}

.context-menu .separator:hover {
    background: rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .right-sidebar {
        width: 250px;
    }
}

@media (max-width: 900px) {
    .right-sidebar {
        width: 220px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }

    .right-sidebar {
        width: 180px;
    }

    .control-buttons {
        gap: 10px;
    }
}

    .control-btn {
        width: 40px;
        height: 40px;
    }

    .play-btn {
        width: 50px;
        height: 50px;
    }

    .album-art {
        width: 120px;
        height: 120px;
    }

    .default-album {
        font-size: 36px;
    }

    .track-info h2 {
        font-size: 18px;
    }

    .track-info p {
        font-size: 12px;
    }
}

/* 高分辨率屏幕优化 */
@media (min-height: 900px) {
    .player-container {
        min-height: 250px;
        max-height: 350px;
    }

    .album-art {
        width: 180px;
        height: 180px;
    }

    .default-album {
        font-size: 54px;
    }
}

/* 低分辨率屏幕优化 */
@media (max-height: 600px) {
    .player-container {
        min-height: 150px;
        max-height: 200px;
    }

    .album-art {
        width: 100px;
        height: 100px;
        margin-bottom: 10px;
    }

    .default-album {
        font-size: 30px;
    }

    .track-info h2 {
        font-size: 16px;
        margin-bottom: 4px;
    }

    .track-info p {
        font-size: 12px;
    }

    .audio-visualizer {
        padding: 10px;
    }
}



/* 任务列表样式 */
.task-list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0;
    background: transparent;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    flex-shrink: 0;
}

.task-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.task-count {
    background: #007acc;
    color: white;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
    font-weight: 600;
}

.task-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px 20px 20px;
}

.task-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.task-item:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.task-item.active {
    background: #e8f5e8;
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    position: relative;
}

.task-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #4caf50;
    border-radius: 0 2px 2px 0;
}

.task-item.active .task-name {
    color: #2e7d32;
    font-weight: 700;
}

/* 移除禁用状态样式，所有任务项都应该可以点击 */

.task-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.task-name {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin: 0 0 6px 0;
}

.task-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.task-patient {
    font-weight: 500;
    color: #495057;
}

.task-duration {
    background: #fff3cd;
    color: #856404;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 11px;
}

.task-progress {
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.task-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    border-radius: 2px;
    transition: width 0.5s ease;
    width: 0%;
    position: relative;
}

.task-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 10px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}

.no-tasks {
    text-align: center;
    padding: 30px 20px;
    color: #999;
}

.no-tasks-icon {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.7;
}

.no-tasks p {
    font-size: 14px;
    margin: 0;
    color: #666;
}

/* 任务切换警告弹窗样式 */
.task-switch-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: modalFadeIn 0.3s ease;
}

.task-switch-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
    animation: modalSlideIn 0.3s ease;
}

.task-switch-modal-header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #e9ecef;
}

.task-switch-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #dc3545;
    text-align: center;
}

.task-switch-modal-body {
    padding: 20px;
    text-align: center;
}

.warning-icon {
    font-size: 48px;
    margin-bottom: 15px;
    animation: warningPulse 1.5s infinite;
}

.task-switch-modal-body p {
    margin: 10px 0;
    font-size: 16px;
    color: #333;
    line-height: 1.5;
}

.current-task-info {
    font-size: 14px !important;
    color: #666 !important;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    margin-top: 15px !important;
}

.task-switch-modal-footer {
    padding: 15px 20px 20px;
    text-align: center;
}

.modal-btn {
    padding: 10px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-btn-primary {
    background: #007bff;
    color: white;
}

.modal-btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes warningPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 任务完成弹窗样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    min-width: 400px;
    max-width: 500px;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 0;
    border-bottom: none;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.modal-close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close-btn:hover {
    background: #f5f5f5;
    color: #333;
}

.modal-body {
    padding: 20px 24px;
}

.task-complete-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.complete-icon {
    font-size: 48px;
    flex-shrink: 0;
}

.complete-text h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.complete-text p {
    margin: 4px 0;
    font-size: 14px;
    color: #666;
}

.complete-message {
    color: #4caf50 !important;
    font-weight: 500;
}

.modal-footer {
    padding: 0 24px 24px;
    text-align: right;
}

.modal-footer .btn {
    min-width: 80px;
}
