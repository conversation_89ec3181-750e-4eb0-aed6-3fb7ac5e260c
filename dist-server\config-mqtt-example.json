{"server": {"host": "0.0.0.0", "port": 8080}, "platform": {"api_base_url": "http://*************", "endpoints": {"report_statistics": "/api/report_play_statistics", "report_completion": "/api/report_task_completion", "get_patient_info": "/api/get_patient_info", "health_check": "/api/health"}, "product_id": "rehabilitation_system", "device_id": "device_001", "token": "1859128349437280256", "timeout": 10000, "retry_attempts": 3, "retry_delay": 1000}, "httpserver": {"host": "0.0.0.0", "port": 8888, "endpoints": {"receive_rehabilitation_info": "/api/receive_rehabilitation_info", "current_visit": "/api/current_visit", "report_play_statistics": "/api/report_play_statistics"}}, "mqtt": {"_comment": "MQTT配置说明 - 符合平台标准协议v2.0", "_enabled_desc": "是否启用MQTT功能，设为false可禁用MQTT", "enabled": true, "_broker_desc": "MQTT服务器配置", "broker_host": "*************", "broker_port": 1883, "_auth_desc": "MQTT认证配置 - 平台标准认证方式", "auth": {"_secure_id_desc": "平台分配的安全ID", "secure_id": "rehabilitation_device_001", "_secure_key_desc": "平台分配的安全密钥", "secure_key": "your_secure_key_here", "_client_id_desc": "MQTT客户端ID，通常与设备实例ID相同", "client_id": "rehabilitation_device_001"}, "_device_desc": "设备标识配置", "device": {"_product_id_desc": "产品ID，由平台分配", "product_id": "rehabilitation_system", "_device_id_desc": "设备ID，唯一标识设备", "device_id": "device_001"}, "_topics_desc": "MQTT主题配置 - 符合平台标准格式", "topics": {"_properties_read_desc": "平台读取设备属性", "properties_read": "/{productId}/{deviceId}/properties/read", "_properties_write_desc": "平台修改设备属性", "properties_write": "/{productId}/{deviceId}/properties/write", "_function_invoke_desc": "平台调用设备功能", "function_invoke": "/{productId}/{deviceId}/function/invoke", "_properties_report_desc": "设备上报属性数据", "properties_report": "/{productId}/{deviceId}/properties/report", "_properties_report_reply_desc": "平台回复属性上报", "properties_report_reply": "/{productId}/{deviceId}/properties/report/reply", "_event_report_desc": "设备上报事件", "event_report": "/{productId}/{deviceId}/event/{eventId}", "_event_report_reply_desc": "平台回复事件上报", "event_report_reply": "/{productId}/{deviceId}/event/{eventId}/reply"}, "_connection_desc": "连接参数配置", "qos": 1, "keep_alive": 60, "clean_session": true, "reconnect_delay": 5}, "heartbeat_interval": 60, "log_level": "INFO", "_config_notes": ["配置文件说明 - 平台标准MQTT协议v2.0：", "1. 设置 mqtt.enabled=false 可禁用MQTT功能，仅使用HTTP", "2. MQTT和HTTP可以同时工作，接收的数据使用相同处理逻辑", "3. 修改 mqtt.broker_host 和 mqtt.broker_port 连接到您的MQTT服务器", "4. 认证方式：username = secureId|timestamp, password = md5(secureId|timestamp|secureKey)", "5. 主题格式：/{productId}/{deviceId}/... 其中productId和deviceId会自动替换", "6. 支持的功能：属性读写、功能调用、事件上报、心跳等", "7. 消息格式包含headers字段，用于传递visitId、patientId等信息", "8. 时间戳使用毫秒级Unix时间戳", "9. 所有消息都是JSON格式，使用UTF-8编码"]}