const { app, BrowserWindow, Menu, dialog, ipcMain, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const { spawn, exec } = require('child_process');
const os = require('os');

// 保持对窗口对象的全局引用
let mainWindow;
let httpServerProcess = null;
let httpServerCheckInterval = null;

// HTTP服务器管理
class HttpServerManager {
  static getHttpServerPath() {
    // 获取httpServer.exe的路径
    let possiblePaths = [];

    // 在打包环境中，httpServer.exe应该在同一目录
    if (app.isPackaged) {
      const appDir = path.dirname(process.execPath);
      possiblePaths.push(path.join(appDir, 'httpServer.exe'));
    } else {
      // 开发环境中的路径
      possiblePaths.push(path.join(__dirname, 'httpServer.exe'));
      possiblePaths.push(path.join(process.cwd(), 'httpServer.exe'));
      possiblePaths.push(path.join(process.cwd(), 'dist', 'httpServer.exe'));
    }

    // 尝试所有可能的路径
    for (const serverPath of possiblePaths) {
      console.log('检查httpServer路径:', serverPath);
      if (fs.existsSync(serverPath)) {
        console.log('找到httpServer.exe:', serverPath);
        return serverPath;
      }
    }

    console.log('httpServer.exe not found in any expected location');
    console.log('尝试的路径:', possiblePaths);
    return null;
  }

  static checkHttpServerProcess() {
    return new Promise((resolve) => {
      // 检查httpServer进程是否运行
      const command = os.platform() === 'win32'
        ? 'tasklist /FI "IMAGENAME eq httpServer.exe" /FO CSV | find /C "httpServer.exe"'
        : 'pgrep -f httpServer';

      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.log('检查httpServer进程失败:', error);
          resolve(false);
          return;
        }

        const isRunning = os.platform() === 'win32'
          ? parseInt(stdout.trim()) > 0
          : stdout.trim().length > 0;

        console.log('httpServer进程状态:', isRunning ? '运行中' : '未运行');
        resolve(isRunning);
      });
    });
  }

  static async startHttpServer() {
    const serverPath = this.getHttpServerPath();
    if (!serverPath) {
      console.error('未找到httpServer.exe文件');
      return false;
    }

    try {
      console.log('启动httpServer:', serverPath);

      // 启动HTTP服务器进程
      httpServerProcess = spawn(serverPath, [], {
        detached: true,
        stdio: 'ignore'
      });

      // 分离进程，让它独立运行
      httpServerProcess.unref();

      // 等待一秒钟检查是否启动成功
      await new Promise(resolve => setTimeout(resolve, 1000));

      const isRunning = await this.checkHttpServerProcess();
      if (isRunning) {
        console.log('httpServer启动成功');
        return true;
      } else {
        console.log('httpServer启动失败');
        return false;
      }
    } catch (error) {
      console.error('启动httpServer异常:', error);
      return false;
    }
  }

  static async ensureHttpServerRunning() {
    const isRunning = await this.checkHttpServerProcess();
    if (!isRunning) {
      console.log('httpServer未运行，正在启动...');
      return await this.startHttpServer();
    }
    return true;
  }

  static startPeriodicCheck() {
    // 每5秒检查一次httpServer进程
    httpServerCheckInterval = setInterval(async () => {
      try {
        await this.ensureHttpServerRunning();
      } catch (error) {
        console.error('定期检查httpServer异常:', error);
      }
    }, 5000);

    console.log('httpServer定期检查已启动 (每5秒)');
  }

  static stopPeriodicCheck() {
    if (httpServerCheckInterval) {
      clearInterval(httpServerCheckInterval);
      httpServerCheckInterval = null;
      console.log('httpServer定期检查已停止');
    }
  }

  static killHttpServer() {
    return new Promise((resolve) => {
      console.log('正在结束httpServer进程...');

      // Windows系统结束httpServer.exe进程
      const command = 'taskkill /F /IM httpServer.exe /T';

      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.log('结束httpServer进程失败或进程不存在:', error.message);
        } else {
          console.log('httpServer进程已结束');
        }
        resolve();
      });
    });
  }
}

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    icon: path.join(__dirname, 'assets/icon.png'),
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false // 允许加载本地文件
    },
    show: false, // 先不显示，等加载完成后再显示
    titleBarStyle: 'default',
    frame: true,
    autoHideMenuBar: true, // 自动隐藏菜单栏
    fullscreen: true // 启动时全屏
  });

  // 加载应用的 index.html
  mainWindow.loadFile('renderer/index.html');

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // 开发环境下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
      mainWindow.webContents.openDevTools();
    }
  });

  // 当窗口被关闭时发出
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 创建菜单 - 已禁用，使用无菜单模式
  // createMenu();

  // 设置空菜单以完全隐藏菜单栏
  Menu.setApplicationMenu(null);
}

function createMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '打开文件',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            openFileDialog();
          }
        },
        {
          label: '刷新媒体库',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.webContents.send('refresh-media-library');
          }
        },
        { type: 'separator' },
        {
          label: 'HTTP服务器状态',
          click: async () => {
            const isRunning = await HttpServerManager.checkHttpServerProcess();
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'HTTP服务器状态',
              message: `HTTP服务器状态: ${isRunning ? '运行中' : '未运行'}`,
              detail: isRunning
                ? 'httpServer.exe 正在运行\n地址: http://localhost:8888'
                : 'httpServer.exe 未运行\n将尝试自动启动'
            });

            if (!isRunning) {
              await HttpServerManager.ensureHttpServerRunning();
            }
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '播放',
      submenu: [
        {
          label: '播放/暂停',
          accelerator: 'Space',
          click: () => {
            mainWindow.webContents.send('toggle-play');
          }
        },
        {
          label: '停止',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow.webContents.send('stop-play');
          }
        },
        {
          label: '上一首',
          accelerator: 'CmdOrCtrl+Left',
          click: () => {
            mainWindow.webContents.send('previous-track');
          }
        },
        {
          label: '下一首',
          accelerator: 'CmdOrCtrl+Right',
          click: () => {
            mainWindow.webContents.send('next-track');
          }
        }
      ]
    },
    {
      label: '视图',
      submenu: [
        {
          label: '全屏',
          accelerator: 'F11',
          click: () => {
            const isFullScreen = mainWindow.isFullScreen();
            mainWindow.setFullScreen(!isFullScreen);
          }
        },
        {
          label: '重新加载',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.reload();
          }
        },
        {
          label: '开发者工具',
          accelerator: 'F12',
          click: () => {
            mainWindow.webContents.toggleDevTools();
          }
        }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于',
              message: '心理康复系统',
              detail: '版本 1.0.0\n专业的心理康复音乐视频播放系统'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// 打开文件对话框
async function openFileDialog() {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile', 'multiSelections'],
    filters: [
      {
        name: '媒体文件',
        extensions: ['mp3', 'mp4', 'avi', 'mkv', 'wav', 'flac', 'aac', 'ogg', 'webm', 'mov', 'wmv']
      },
      {
        name: '音频文件',
        extensions: ['mp3', 'wav', 'flac', 'aac', 'ogg']
      },
      {
        name: '视频文件',
        extensions: ['mp4', 'avi', 'mkv', 'webm', 'mov', 'wmv']
      },
      {
        name: '所有文件',
        extensions: ['*']
      }
    ]
  });

  if (!result.canceled && result.filePaths.length > 0) {
    mainWindow.webContents.send('files-selected', result.filePaths);
  }
}

// 打开文件夹对话框
async function openFolderDialog() {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory']
  });

  if (!result.canceled && result.filePaths.length > 0) {
    const folderPath = result.filePaths[0];
    const mediaFiles = scanMediaFiles(folderPath);
    mainWindow.webContents.send('folder-selected', mediaFiles);
  }
}

// 获取媒体目录路径
function getMediaDirectoryPaths() {
  let audioDir, videoDir;

  if (app.isPackaged) {
    // 打包环境中，audio和video目录在应用程序目录下
    const appDir = path.dirname(process.execPath);
    audioDir = path.join(appDir, 'audio');
    videoDir = path.join(appDir, 'video');
  } else {
    // 开发环境中，audio和video目录在项目根目录下
    audioDir = path.join(__dirname, 'audio');
    videoDir = path.join(__dirname, 'video');
  }

  return { audioDir, videoDir };
}

// 扫描内置媒体文件
function scanBuiltinMedia() {
  const { audioDir, videoDir } = getMediaDirectoryPaths();

  const audioFiles = scanMediaDirectory(audioDir, 'audio');
  const videoFiles = scanMediaDirectory(videoDir, 'video');

  return {
    audio: audioFiles,
    video: videoFiles
  };
}

// 扫描媒体目录并按子目录分类
function scanMediaDirectory(baseDir, mediaType) {
  const mediaExtensions = {
    audio: ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'],
    video: ['.mp4', '.avi', '.mkv', '.webm', '.mov', '.wmv', '.flv']
  };

  const categories = {};

  try {
    console.log(`🔍 开始扫描${mediaType}目录: ${baseDir}`);

    if (!fs.existsSync(baseDir)) {
      console.log(`❌ 目录不存在: ${baseDir}`);
      return categories;
    }

    const items = fs.readdirSync(baseDir);
    console.log(`📂 找到${items.length}个项目:`, items);

    for (const item of items) {
      const fullPath = path.join(baseDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // 子目录作为分类
        const categoryName = item;
        console.log(`📁 扫描分类目录: ${categoryName}`);
        const categoryFiles = scanCategoryDirectory(fullPath, mediaExtensions[mediaType], mediaType);
        console.log(`  找到${categoryFiles.length}个${mediaType}文件`);

        if (categoryFiles.length > 0) {
          categories[categoryName] = categoryFiles;
          console.log(`✅ 添加分类: ${categoryName} (${categoryFiles.length}个文件)`);
        }
      } else if (stat.isFile()) {
        // 根目录下的文件归入"默认"分类
        const ext = path.extname(item).toLowerCase();
        if (mediaExtensions[mediaType].includes(ext)) {
          if (!categories['默认']) {
            categories['默认'] = [];
          }
          categories['默认'].push({
            name: item,
            path: fullPath,
            size: stat.size,
            extension: ext,
            type: mediaType
          });
        }
      }
    }
  } catch (error) {
    console.error(`扫描${mediaType}目录错误:`, error);
  }

  return categories;
}

// 扫描分类目录中的文件
function scanCategoryDirectory(dirPath, allowedExtensions, mediaType) {
  const files = [];

  try {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isFile()) {
        const ext = path.extname(item).toLowerCase();
        if (allowedExtensions.includes(ext)) {
          files.push({
            name: item,
            path: fullPath,
            size: stat.size,
            extension: ext,
            type: mediaType // 使用正确的媒体类型
          });
        }
      } else if (stat.isDirectory()) {
        // 递归扫描子目录
        const subFiles = scanCategoryDirectory(fullPath, allowedExtensions, mediaType);
        files.push(...subFiles);
      }
    }
  } catch (error) {
    console.error('扫描分类目录错误:', error);
  }

  return files;
}

// IPC 事件处理
ipcMain.handle('get-file-info', async (event, filePath) => {
  try {
    const stat = fs.statSync(filePath);
    const info = {
      name: path.basename(filePath),
      size: stat.size,
      path: filePath,
      extension: path.extname(filePath).toLowerCase()
    };
    return info;
  } catch (error) {
    console.error('获取文件信息错误:', error);
    return null;
  }
});

// 获取内置媒体列表
ipcMain.handle('get-builtin-media', async () => {
  try {
    const { audioDir, videoDir } = getMediaDirectoryPaths();
    console.log('🔍 媒体目录路径:');
    console.log('  音频目录:', audioDir);
    console.log('  视频目录:', videoDir);
    console.log('  音频目录存在:', fs.existsSync(audioDir));
    console.log('  视频目录存在:', fs.existsSync(videoDir));

    const mediaData = scanBuiltinMedia();
    console.log('📁 扫描到的媒体文件:');
    console.log('  音频分类数:', Object.keys(mediaData.audio).length);
    console.log('  视频分类数:', Object.keys(mediaData.video).length);
    console.log('  音频详情:', mediaData.audio);
    console.log('  视频详情:', mediaData.video);

    return mediaData;
  } catch (error) {
    console.error('❌ 获取内置媒体列表错误:', error);
    return { audio: {}, video: {} };
  }
});

// 创建媒体目录
ipcMain.handle('create-media-directories', async () => {
  try {
    const { audioDir, videoDir } = getMediaDirectoryPaths();

    if (!fs.existsSync(audioDir)) {
      fs.mkdirSync(audioDir, { recursive: true });
      console.log('创建音频目录:', audioDir);
    }

    if (!fs.existsSync(videoDir)) {
      fs.mkdirSync(videoDir, { recursive: true });
      console.log('创建视频目录:', videoDir);
    }

    return { success: true };
  } catch (error) {
    console.error('创建媒体目录错误:', error);
    return { success: false, error: error.message };
  }
});

// 打开媒体文件夹
ipcMain.handle('open-media-folder', async () => {
  try {
    let mediaPath;
    if (app.isPackaged) {
      // 打包环境中，打开应用程序目录
      mediaPath = path.dirname(process.execPath);
    } else {
      // 开发环境中，打开项目根目录
      mediaPath = __dirname;
    }
    await shell.openPath(mediaPath);
    return { success: true };
  } catch (error) {
    console.error('打开媒体文件夹错误:', error);
    return { success: false, error: error.message };
  }
});

// 获取配置文件路径
function getConfigPath() {
  // 在开发环境中，配置文件在项目根目录
  // 在打包后，配置文件应该在应用的外部目录（与exe同级）
  if (app.isPackaged) {
    // 打包后：配置文件在应用目录下
    return path.join(path.dirname(process.execPath), 'config.json');
  } else {
    // 开发环境：配置文件在项目根目录
    return path.join(__dirname, 'config.json');
  }
}

// 确保配置文件存在
function ensureConfigFile() {
  const configPath = getConfigPath();

  // 如果配置文件不存在，创建默认配置文件
  if (!fs.existsSync(configPath)) {
    const defaultConfig = {
      platform: {
        api_base_url: "http://localhost:3000",
        endpoints: {
          report_statistics: "/api/report_play_statistics",
          report_completion: "/api/report_task_completion",
          get_patient_info: "/api/get_patient_info",
          health_check: "/api/health"
        },
        product_id: "rehabilitation_system",
        device_id: "device_001",
        token: "your_token_here",
        timeout: 10000,
        retry_attempts: 3,
        retry_delay: 1000
      },
      httpserver: {
        host: "0.0.0.0",
        port: 8888,
        endpoints: {
          receive_rehabilitation_info: "/api/receive_rehabilitation_info",
          current_visit: "/api/current_visit",
          report_play_statistics: "/api/report_play_statistics"
        }
      },
      heartbeat_interval: 60,
      log_level: "INFO"
    };

    try {
      fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 2), 'utf8');
      console.log('已创建默认配置文件:', configPath);
    } catch (error) {
      console.error('创建默认配置文件失败:', error);
    }
  }

  return configPath;
}

// 加载配置文件
ipcMain.handle('load-config', async () => {
  try {
    const configPath = ensureConfigFile();
    const configData = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configData);
    console.log('配置文件加载成功:', configPath);
    console.log('应用是否已打包:', app.isPackaged);
    return config;
  } catch (error) {
    console.error('配置文件加载失败:', error);
    // 返回默认配置
    return {
      platform: {
        api_base_url: "http://localhost:3000",
        endpoints: {
          report_statistics: "/api/report_play_statistics",
          report_completion: "/api/report_task_completion"
        },
        timeout: 10000
      },
      httpserver: {
        host: "0.0.0.0",
        port: 8888,
        endpoints: {
          receive_rehabilitation_info: "/api/receive_rehabilitation_info",
          current_visit: "/api/current_visit",
          report_play_statistics: "/api/report_play_statistics"
        }
      }
    };
  }
});

// 获取配置文件路径
ipcMain.handle('get-config-path', async () => {
  return getConfigPath();
});

// 窗口控制
ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('close-window', async () => {
  if (mainWindow) {
    // 先停止httpServer检查和结束进程
    HttpServerManager.stopPeriodicCheck();
    await HttpServerManager.killHttpServer();

    // 然后关闭窗口
    mainWindow.close();
  }
});

// 应用程序事件
app.whenReady().then(async () => {
  // 首先确保httpServer运行
  console.log('检查并启动httpServer...');
  await HttpServerManager.ensureHttpServerRunning();

  // 启动定期检查
  HttpServerManager.startPeriodicCheck();

  // 创建主窗口
  createWindow();
});

app.on('window-all-closed', () => {
  // 停止httpServer定期检查
  HttpServerManager.stopPeriodicCheck();

  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// 应用退出前清理
app.on('before-quit', () => {
  console.log('应用即将退出，停止httpServer检查...');
  HttpServerManager.stopPeriodicCheck();
  HttpServerManager.killHttpServer();
});

// 安全设置
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});
