#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT依赖安装脚本
自动安装paho-mqtt库
"""

import subprocess
import sys
import os

def check_mqtt_installed():
    """检查MQTT库是否已安装"""
    try:
        import paho.mqtt.client as mqtt
        print("✅ paho-mqtt 库已安装")
        return True
    except ImportError:
        print("❌ paho-mqtt 库未安装")
        return False

def install_mqtt():
    """安装MQTT库"""
    try:
        print("📦 正在安装 paho-mqtt 库...")
        
        # 使用pip安装
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "paho-mqtt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ paho-mqtt 库安装成功")
            print("📄 安装输出:")
            print(result.stdout)
            return True
        else:
            print("❌ paho-mqtt 库安装失败")
            print("📄 错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 安装过程中发生异常: {e}")
        return False

def create_install_bat():
    """创建Windows批处理安装脚本"""
    bat_content = """@echo off
echo 正在安装MQTT依赖库...
python -m pip install paho-mqtt
if %errorlevel% == 0 (
    echo MQTT库安装成功!
) else (
    echo MQTT库安装失败!
)
pause
"""
    
    try:
        with open("安装MQTT依赖.bat", "w", encoding="utf-8") as f:
            f.write(bat_content)
        print("✅ 已创建 '安装MQTT依赖.bat' 文件")
        return True
    except Exception as e:
        print(f"❌ 创建批处理文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 MQTT依赖安装工具")
    print("=" * 40)
    
    # 检查当前状态
    if check_mqtt_installed():
        print("💡 MQTT库已经安装，无需重复安装")
        return
    
    print("🚀 开始安装MQTT依赖...")
    
    # 安装MQTT库
    if install_mqtt():
        print("\n🎉 安装完成!")
        
        # 再次检查
        if check_mqtt_installed():
            print("✅ 验证成功: MQTT库可以正常导入")
        else:
            print("⚠️  警告: 安装完成但无法导入，可能需要重启Python环境")
    else:
        print("\n❌ 安装失败!")
        print("💡 建议:")
        print("   1. 检查网络连接")
        print("   2. 确保pip工具正常工作")
        print("   3. 尝试手动运行: pip install paho-mqtt")
    
    # 创建批处理文件
    print("\n📝 创建便捷安装脚本...")
    create_install_bat()
    
    print("\n" + "=" * 40)
    print("🏁 安装程序结束")

if __name__ == '__main__':
    main()
