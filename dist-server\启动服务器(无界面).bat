@echo off
chcp 65001 >nul
echo 康复系统HTTP/MQTT双协议服务器 (无界面版本)
echo ================================================
echo.
echo 正在启动无界面服务器...
echo 注意：这是无界面模式，服务器将在后台运行
echo 所有日志将保存到 logs 目录中
echo.
echo HTTP服务器地址: http://localhost:8888
echo MQTT协议: 支持平台标准MQTT协议v2.0
echo 编码映射: 支持137个音视频文件编码自动映射
echo.
echo 要停止服务器，请关闭此窗口或在任务管理器中结束进程
echo ================================================

start "" "RehabilitationServer.exe"

echo.
echo 无界面服务器已在后台启动
echo 可以通过任务管理器查看 RehabilitationServer.exe 进程
echo 日志文件位置: logs\当前日期\log.txt
pause
