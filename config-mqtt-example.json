{"server": {"host": "0.0.0.0", "port": 8080}, "platform": {"api_base_url": "http://*************", "endpoints": {"report_statistics": "/api/report_play_statistics", "report_completion": "/api/report_task_completion", "get_patient_info": "/api/get_patient_info", "health_check": "/api/health"}, "product_id": "rehabilitation_system", "device_id": "device_001", "token": "1859128349437280256", "timeout": 10000, "retry_attempts": 3, "retry_delay": 1000}, "httpserver": {"host": "0.0.0.0", "port": 8888, "endpoints": {"receive_rehabilitation_info": "/api/receive_rehabilitation_info", "current_visit": "/api/current_visit", "report_play_statistics": "/api/report_play_statistics"}}, "mqtt": {"_comment": "MQTT配置说明", "_enabled_desc": "是否启用MQTT功能，设为false可禁用MQTT", "enabled": true, "_broker_desc": "MQTT服务器配置", "broker_host": "*************", "broker_port": 1883, "_auth_desc": "MQTT认证配置，如不需要认证可留空", "username": "", "password": "", "_client_desc": "MQTT客户端配置", "client_id": "rehabilitation_device_001", "clean_session": true, "keep_alive": 60, "reconnect_delay": 5, "_topics_desc": "MQTT主题配置", "topics": {"_receive_info_desc": "接收康复信息的主题", "receive_rehabilitation_info": "rehabilitation/device_001/receive_info", "_report_stats_desc": "上报统计数据的主题（暂未使用）", "report_statistics": "rehabilitation/device_001/report_stats", "_heartbeat_desc": "心跳主题", "heartbeat": "rehabilitation/device_001/heartbeat"}, "_qos_desc": "消息质量等级：0=最多一次，1=至少一次，2=恰好一次", "qos": 1}, "heartbeat_interval": 60, "log_level": "INFO", "_config_notes": ["配置文件说明：", "1. 设置 mqtt.enabled=false 可禁用MQTT功能，仅使用HTTP", "2. MQTT和HTTP可以同时工作，接收的数据使用相同处理逻辑", "3. 修改 mqtt.broker_host 和 mqtt.broker_port 连接到您的MQTT服务器", "4. 根据需要修改 mqtt.topics 中的主题名称", "5. 如果MQTT服务器需要认证，请填写 username 和 password"]}