('D:\\xinliceping\\Player\\build-server\\rehabilitation_server_no_console\\PYZ-00.pyz',
 [('__future__', 'C:\\Python39\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python39\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'C:\\Python39\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python39\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python39\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python39\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python39\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'C:\\Python39\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Python39\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\Python39\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Python39\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Python39\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Python39\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Python39\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Python39\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('chardet',
   'C:\\Python39\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Python39\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Python39\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Python39\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Python39\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'C:\\Python39\\lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Python39\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Python39\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Python39\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Python39\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Python39\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Python39\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Python39\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Python39\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'C:\\Python39\\lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Python39\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Python39\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Python39\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Python39\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\Python39\\lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Python39\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Python39\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Python39\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Python39\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'C:\\Python39\\lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Python39\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Python39\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'C:\\Python39\\lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Python39\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Python39\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Python39\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Python39\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Python39\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Python39\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Python39\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Python39\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Python39\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Python39\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python39\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python39\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python39\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Python39\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Python39\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Python39\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Python39\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Python39\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Python39\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Python39\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Python39\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Python39\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Python39\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Python39\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Python39\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Python39\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('csv', 'C:\\Python39\\lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python39\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Python39\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Python39\\lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\Python39\\lib\\dis.py', 'PYMODULE'),
  ('email', 'C:\\Python39\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python39\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python39\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'C:\\Python39\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'C:\\Python39\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\Python39\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python39\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python39\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'C:\\Python39\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python39\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python39\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python39\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'C:\\Python39\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python39\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'C:\\Python39\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'C:\\Python39\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python39\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python39\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'C:\\Python39\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python39\\lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python39\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Python39\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python39\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Python39\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Python39\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Python39\\lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'C:\\Python39\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python39\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Python39\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Python39\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python39\\lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'C:\\Python39\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Python39\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python39\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'C:\\Python39\\lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'C:\\Python39\\lib\\http\\server.py', 'PYMODULE'),
  ('idna', 'C:\\Python39\\lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'C:\\Python39\\lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'C:\\Python39\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Python39\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Python39\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Python39\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp', 'C:\\Python39\\lib\\imp.py', 'PYMODULE'),
  ('importlib', 'C:\\Python39\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python39\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python39\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common', 'C:\\Python39\\lib\\importlib\\_common.py', 'PYMODULE'),
  ('importlib.abc', 'C:\\Python39\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python39\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python39\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python39\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python39\\lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'C:\\Python39\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python39\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\Python39\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python39\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python39\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python39\\lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\Python39\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'C:\\Python39\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python39\\lib\\mimetypes.py', 'PYMODULE'),
  ('netrc', 'C:\\Python39\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python39\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Python39\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Python39\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Python39\\lib\\optparse.py', 'PYMODULE'),
  ('paho', 'C:\\Python39\\lib\\site-packages\\paho\\__init__.py', 'PYMODULE'),
  ('paho.mqtt',
   'C:\\Python39\\lib\\site-packages\\paho\\mqtt\\__init__.py',
   'PYMODULE'),
  ('paho.mqtt.client',
   'C:\\Python39\\lib\\site-packages\\paho\\mqtt\\client.py',
   'PYMODULE'),
  ('paho.mqtt.enums',
   'C:\\Python39\\lib\\site-packages\\paho\\mqtt\\enums.py',
   'PYMODULE'),
  ('paho.mqtt.matcher',
   'C:\\Python39\\lib\\site-packages\\paho\\mqtt\\matcher.py',
   'PYMODULE'),
  ('paho.mqtt.packettypes',
   'C:\\Python39\\lib\\site-packages\\paho\\mqtt\\packettypes.py',
   'PYMODULE'),
  ('paho.mqtt.properties',
   'C:\\Python39\\lib\\site-packages\\paho\\mqtt\\properties.py',
   'PYMODULE'),
  ('paho.mqtt.reasoncodes',
   'C:\\Python39\\lib\\site-packages\\paho\\mqtt\\reasoncodes.py',
   'PYMODULE'),
  ('paho.mqtt.subscribeoptions',
   'C:\\Python39\\lib\\site-packages\\paho\\mqtt\\subscribeoptions.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Python39\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Python39\\lib\\pickle.py', 'PYMODULE'),
  ('platform', 'C:\\Python39\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Python39\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python39\\lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'C:\\Python39\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Python39\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Python39\\lib\\random.py', 'PYMODULE'),
  ('requests',
   'C:\\Python39\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Python39\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Python39\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Python39\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Python39\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Python39\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Python39\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Python39\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Python39\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Python39\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Python39\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Python39\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Python39\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Python39\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Python39\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Python39\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Python39\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('selectors', 'C:\\Python39\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'C:\\Python39\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Python39\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Python39\\lib\\signal.py', 'PYMODULE'),
  ('simplejson',
   'C:\\Python39\\lib\\site-packages\\simplejson\\__init__.py',
   'PYMODULE'),
  ('simplejson.compat',
   'C:\\Python39\\lib\\site-packages\\simplejson\\compat.py',
   'PYMODULE'),
  ('simplejson.decoder',
   'C:\\Python39\\lib\\site-packages\\simplejson\\decoder.py',
   'PYMODULE'),
  ('simplejson.encoder',
   'C:\\Python39\\lib\\site-packages\\simplejson\\encoder.py',
   'PYMODULE'),
  ('simplejson.errors',
   'C:\\Python39\\lib\\site-packages\\simplejson\\errors.py',
   'PYMODULE'),
  ('simplejson.ordered_dict',
   'C:\\Python39\\lib\\site-packages\\simplejson\\ordered_dict.py',
   'PYMODULE'),
  ('simplejson.raw_json',
   'C:\\Python39\\lib\\site-packages\\simplejson\\raw_json.py',
   'PYMODULE'),
  ('simplejson.scanner',
   'C:\\Python39\\lib\\site-packages\\simplejson\\scanner.py',
   'PYMODULE'),
  ('socket', 'C:\\Python39\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python39\\lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'C:\\Python39\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Python39\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Python39\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python39\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python39\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python39\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python39\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python39\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Python39\\lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Python39\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python39\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python39\\lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'C:\\Python39\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Python39\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python39\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python39\\lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python39\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python39\\lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python39\\lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'C:\\Python39\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Python39\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Python39\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Python39\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Python39\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Python39\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Python39\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Python39\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Python39\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Python39\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Python39\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Python39\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Python39\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Python39\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Python39\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Python39\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Python39\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Python39\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Python39\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Python39\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Python39\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'C:\\Python39\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'C:\\Python39\\lib\\uuid.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python39\\lib\\zipfile.py', 'PYMODULE')])
