#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音视频编码映射测试脚本
测试HTTP/MQTT双协议服务器的编码处理功能
"""

import json
import requests
import time
from datetime import datetime

# 配置信息
HTTP_SERVER_URL = 'http://localhost:8888'

def create_test_data_with_codes():
    """创建包含编码的测试数据"""
    return {
        "company": "编码测试医院",
        "assessment": "音乐视频疗法量表",
        "basic_info": {
            "id": "20240616_CODE_TEST",
            "department": "心理康复科",
            "name": "编码测试患者",
            "gender": "男",
            "age": 30
        },
        "training_task": {
            "training_project": "编码映射测试训练",
            "training_duration": 30,
            "training_frequency": 1,
            # 测试单个编码
            "media_code": "72539895",  # 应该映射到 "1.止损负面情绪.mp3"
            "audio_code": "45577303",  # 应该映射到 "1.你是爱和光.mp3"
            "video_code": "33326899",  # 应该映射到 "5分钟缓解焦虑.mp4"
            # 测试编码列表
            "media_codes": "35615503,44704557,47791818",  # 呼吸系列
            # 测试媒体列表
            "media_list": [
                {"code": "69985783", "name": ""},  # 爱我的灵魂.mp3
                {"code": "12314605", "name": ""},  # 爱我的内心.mp3
                {"code": "41070070", "name": ""}   # 爱我的身体.mp3
            ]
        },
        "training_effect": {
            "completed_project": "",
            "completed_duration": 0,
            "completed_frequency": 0
        },
        "signature": "编码测试医生",
        "report_date": datetime.now().strftime("%Y-%m-%d"),
        "source": "code_mapping_test",
        # 测试根级别编码
        "item_code": "77055539",  # 等待.mp3
        "content_code": "51993412"  # 公交地铁.mp3
    }

def create_mqtt_test_data():
    """创建MQTT格式的测试数据"""
    rehabilitation_info = create_test_data_with_codes()
    
    return {
        "timestamp": int(time.time() * 1000),
        "messageId": f"code_test_{int(time.time() * 1000)}",
        "deviceId": "device_001",
        "functionId": "start_rehabilitation",
        "headers": {
            "visitId": "code_test_visit_001",
            "patientId": "code_test_patient_001",
            "visitNo": "CODE_TEST_001",
            "itemId": "code_test_item_001",
            "itemType": "1"
        },
        "inputs": [
            {
                "name": "rehabilitation_info",
                "value": json.dumps(rehabilitation_info, ensure_ascii=False)
            }
        ]
    }

def test_http_code_mapping():
    """测试HTTP协议的编码映射"""
    print("\n🌐 测试HTTP协议编码映射")
    print("-" * 50)
    
    try:
        # 发送包含编码的康复信息
        test_data = create_test_data_with_codes()
        
        print("📤 发送包含编码的康复信息...")
        print("🔢 测试编码:")
        print(f"   media_code: {test_data['training_task']['media_code']}")
        print(f"   audio_code: {test_data['training_task']['audio_code']}")
        print(f"   video_code: {test_data['training_task']['video_code']}")
        print(f"   media_codes: {test_data['training_task']['media_codes']}")
        print(f"   item_code: {test_data['item_code']}")
        print(f"   content_code: {test_data['content_code']}")
        
        response = requests.post(
            f"{HTTP_SERVER_URL}/api/receive_rehabilitation_info",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ HTTP编码数据发送成功")
            result = response.json()
            print(f"📄 服务器响应: {result.get('message', '')}")
            
            # 等待处理
            time.sleep(2)
            
            # 查询处理结果
            print("🔍 查询处理结果...")
            visit_response = requests.get(f"{HTTP_SERVER_URL}/api/current_visit", timeout=5)
            if visit_response.status_code == 200:
                visit_data = visit_response.json()
                print("✅ 编码处理结果:")
                print(f"   患者姓名: {visit_data.get('patient_name', '')}")
                print(f"   康复内容: {visit_data.get('rehabilitation_content', [])}")
                
                # 检查原始数据中的编码映射
                raw_data = visit_data.get('raw_data', {})
                training_task = raw_data.get('training_task', {})
                
                print("🎯 编码映射结果:")
                for field in ['media_code_name', 'audio_code_name', 'video_code_name']:
                    if field in training_task:
                        print(f"   {field}: {training_task[field]}")
                
                if 'media_names' in training_task:
                    print(f"   编码列表映射: {training_task['media_names']}")
                
            return True
        else:
            print(f"❌ HTTP编码数据发送失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ HTTP编码映射测试异常: {e}")
        return False

def test_code_lookup():
    """测试编码查找功能"""
    print("\n🔍 测试编码查找功能")
    print("-" * 50)
    
    # 测试编码
    test_codes = [
        "72539895",  # 1.止损负面情绪.mp3
        "45577303",  # 1.你是爱和光.mp3
        "33326899",  # 5分钟缓解焦虑.mp4
        "99999999",  # 不存在的编码
        "35615503"   # 1.呼吸介绍.mp3
    ]
    
    # 导入编码映射（模拟服务器端的映射）
    from http_server import get_media_name_by_code, get_media_code_by_name
    
    print("📋 编码到文件名映射测试:")
    for code in test_codes:
        name = get_media_name_by_code(code)
        print(f"   {code} -> {name}")
    
    print("\n📋 文件名到编码映射测试:")
    test_names = [
        "1.止损负面情绪.mp3",
        "1.你是爱和光.mp3",
        "5分钟缓解焦虑.mp4",
        "不存在的文件.mp3"
    ]
    
    for name in test_names:
        code = get_media_code_by_name(name)
        print(f"   {name} -> {code}")

def test_server_status():
    """测试服务器状态"""
    print("\n🔧 测试服务器状态")
    print("-" * 50)
    
    try:
        response = requests.get(f"{HTTP_SERVER_URL}/api/status", timeout=5)
        if response.status_code == 200:
            print("✅ HTTP服务器运行正常")
            return True
        else:
            print(f"❌ HTTP服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接HTTP服务器: {e}")
        return False

def main():
    """主函数"""
    print("🧪 音视频编码映射测试工具")
    print("=" * 60)
    
    # 测试服务器状态
    if not test_server_status():
        print("\n❌ 服务器未运行，请先启动康复系统服务器")
        return
    
    # 测试编码查找功能
    try:
        test_code_lookup()
    except ImportError:
        print("⚠️ 无法导入服务器模块，跳过编码查找测试")
    
    # 测试HTTP编码映射
    http_result = test_http_code_mapping()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("-" * 60)
    
    if http_result:
        print("✅ HTTP编码映射测试: 通过")
        print("\n🎉 编码映射功能正常！")
        print("💡 说明:")
        print("   - 服务器能正确处理音视频编码")
        print("   - 编码会自动映射为对应的文件名")
        print("   - 前端可以通过HTTP协议获取处理后的数据")
        print("   - 支持单个编码、编码列表、媒体列表等多种格式")
    else:
        print("❌ HTTP编码映射测试: 失败")
        print("\n⚠️ 请检查:")
        print("   1. 服务器是否正常运行")
        print("   2. 编码映射表是否正确加载")
        print("   3. 数据处理逻辑是否正常")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
